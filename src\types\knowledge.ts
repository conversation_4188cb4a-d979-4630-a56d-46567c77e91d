export interface KnowledgeCategory {
  id: string
  name: string
  description?: string
  icon?: string
  color?: string
  parentId?: string
  children?: KnowledgeCategory[]
  createdAt: Date
  updatedAt: Date
}

export interface KnowledgeDocument {
  id: string
  title: string
  content: string
  summary?: string
  categoryId: string
  tags: string[]
  author: string
  status: 'draft' | 'published' | 'archived'
  isPublic: boolean
  viewCount: number
  likeCount: number
  createdAt: Date
  updatedAt: Date
  lastEditedBy: string
}

export interface KnowledgeTag {
  id: string
  name: string
  color: string
  count: number
}

export interface SearchResult {
  documents: KnowledgeDocument[]
  total: number
  page: number
  pageSize: number
}

export interface KnowledgeStats {
  totalDocuments: number
  totalCategories: number
  totalTags: number
  recentDocuments: KnowledgeDocument[]
  popularDocuments: KnowledgeDocument[]
}
