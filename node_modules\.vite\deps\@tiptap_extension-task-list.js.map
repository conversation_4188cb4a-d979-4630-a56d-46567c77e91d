{"version": 3, "sources": ["../../@tiptap/extension-task-list/src/task-list.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TaskListOptions {\n  /**\n   * The node type name for a task item.\n   * @default 'taskItem'\n   * @example 'myCustomTaskItem'\n   */\n  itemTypeName: string,\n\n  /**\n   * The HTML attributes for a task list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    taskList: {\n      /**\n       * Toggle a task list\n       * @example editor.commands.toggleTaskList()\n       */\n      toggleTaskList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create task lists.\n * @see https://www.tiptap.dev/api/nodes/task-list\n */\nexport const TaskList = Node.create<TaskListOptions>({\n  name: 'taskList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'taskItem',\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `ul[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-type': this.name }), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleTaskList: () => ({ commands }) => {\n        return commands.toggleList(this.name, this.options.itemTypeName)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-9': () => this.editor.commands.toggleTaskList(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;AAkCa,IAAA,WAAW,KAAK,OAAwB;EACnD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,cAAc;MACd,gBAAgB,CAAA;;;EAIpB,OAAO;EAEP,UAAO;AACL,WAAO,GAAG,KAAK,QAAQ,YAAY;;EAGrC,YAAS;AACP,WAAO;MACL;QACE,KAAK,iBAAiB,KAAK,IAAI;QAC/B,UAAU;MACX;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,gBAAgB,EAAE,aAAa,KAAK,KAAI,CAAE,GAAG,CAAC;;EAG3G,cAAW;AACT,WAAO;MACL,gBAAgB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACrC,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,YAAY;;;;EAKrE,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,eAAc;;;AAG7D,CAAA;", "names": []}