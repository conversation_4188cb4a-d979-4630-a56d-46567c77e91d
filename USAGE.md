# 知识库系统使用指南

## 🎯 系统概览

这是一个现代化的知识库管理系统，提供了完整的文档创建、编辑、管理和搜索功能。系统采用了类似 Notion 和语雀的设计理念，注重用户体验和操作效率。

## 🚀 快速上手

### 1. 启动应用
```bash
npm install
npm run dev
```
访问 http://localhost:5173 即可开始使用。

### 2. 界面布局

#### 顶部导航栏
- **应用标题**：显示"知识库管理"
- **新建文档**：快速创建新文档
- **设置**：系统设置和个性化配置

#### 左侧边栏
- **搜索框**：全文搜索文档
- **新建文档按钮**：创建新文档
- **知识分类**：树形结构的分类导航
- **热门标签**：常用标签快速筛选
- **最近编辑**：最近修改的文档列表

#### 主内容区
- **仪表板**：数据统计和快速操作
- **文档列表**：当前分类下的文档
- **文档详情**：文档内容展示
- **文档编辑**：富文本编辑界面

## 📝 核心功能

### 仪表板
仪表板是系统的首页，提供了：

1. **欢迎区域**
   - 个性化欢迎信息
   - 快速新建文档按钮

2. **统计卡片**
   - 总文档数：显示系统中的文档总数
   - 分类数量：显示分类的总数
   - 标签数量：显示标签的总数
   - 总浏览量：所有文档的浏览量总和

3. **最近编辑**
   - 显示最近修改的5篇文档
   - 包含文档标题、摘要、作者和更新时间
   - 点击可直接跳转到文档详情

4. **热门文档**
   - 按浏览量排序的热门文档
   - 显示排名、标题和统计数据
   - 点击可直接查看文档

5. **快速操作**
   - 新建文档：创建新的知识文档
   - 新建分类：创建新的文档分类
   - 导入文档：批量导入已有文档
   - 导出备份：导出所有文档数据

### 文档管理

#### 创建文档
1. 点击"新建文档"按钮
2. 进入编辑器界面
3. 输入文档标题
4. 选择文档分类
5. 添加标签
6. 编写文档内容
7. 保存或发布

#### 编辑文档
1. 在文档列表或详情页点击"编辑"
2. 使用富文本编辑器修改内容
3. 支持以下格式：
   - 标题（H1-H3）
   - 粗体、斜体、删除线
   - 有序列表、无序列表
   - 任务列表
   - 引用块
   - 代码块
   - 高亮文本

#### 文档设置
在编辑器右侧面板可以设置：
- **基本信息**：分类、标签、摘要
- **发布状态**：草稿/已发布
- **可见性**：公开/私有
- **文档统计**：字数、阅读时间等

### 分类管理

#### 分类结构
- 支持多级分类（父分类-子分类）
- 树形展示，清晰的层级关系
- 每个分类显示包含的文档数量

#### 分类操作
- 点击分类名称：查看该分类下的所有文档
- 支持拖拽排序（开发中）
- 新建子分类（开发中）

### 搜索功能

#### 全文搜索
- 在左侧边栏搜索框输入关键词
- 支持搜索文档标题、内容和标签
- 实时显示搜索结果

#### 标签筛选
- 点击热门标签进行筛选
- 支持多标签组合筛选
- 已选标签会高亮显示

#### 高级筛选
在文档列表页面可以：
- 按更新时间、创建时间、浏览量等排序
- 切换列表视图和卡片视图
- 按分类筛选文档

### 文档详情

#### 文档信息
- 文档标题和内容展示
- 作者、更新时间、浏览量等元信息
- 文档状态和标签
- 面包屑导航

#### 交互功能
- 点赞：为文档点赞
- 分享：分享文档链接
- 收藏：收藏到个人收藏夹
- 评论：发表和查看评论

#### 目录导航
- 自动生成文档目录
- 点击目录项快速跳转
- 适用于长文档的快速导航

#### 相关文档
- 基于标签和分类的智能推荐
- 显示相关文档的标题和摘要
- 点击可快速跳转

## ⚙️ 系统设置

点击顶部导航栏的"设置"按钮可以配置：

### 主题设置
- **浅色主题**：适合白天使用
- **深色主题**：适合夜间使用
- **跟随系统**：自动切换主题

### 默认视图
- **仪表板**：启动时显示仪表板
- **文档列表**：启动时显示文档列表

### 编辑器设置
- **自动保存**：开启/关闭自动保存功能
- 自动保存间隔：30秒

## 💡 使用技巧

### 快速操作
1. **快速新建**：使用顶部或侧边栏的新建按钮
2. **快速搜索**：使用侧边栏搜索框
3. **快速分类**：点击侧边栏分类树
4. **快速标签**：点击热门标签进行筛选

### 编辑技巧
1. **Markdown支持**：可以使用Markdown语法快速格式化
2. **自动保存**：编辑时会自动保存草稿
3. **快捷键**：支持常用的编辑快捷键
4. **实时预览**：编辑时可以实时看到效果

### 组织技巧
1. **合理分类**：建立清晰的分类结构
2. **标签管理**：使用标签进行横向分类
3. **摘要编写**：为文档编写简洁的摘要
4. **定期整理**：定期整理和更新文档

## 🔧 故障排除

### 常见问题

1. **页面加载缓慢**
   - 检查网络连接
   - 清除浏览器缓存
   - 重启开发服务器

2. **编辑器无法使用**
   - 刷新页面
   - 检查浏览器兼容性
   - 查看控制台错误信息

3. **搜索无结果**
   - 检查搜索关键词
   - 确认文档是否已发布
   - 尝试使用不同的搜索词

### 技术支持

如果遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 检查网络连接状态
3. 重启应用服务
4. 联系技术支持团队

## 📈 最佳实践

### 文档编写
1. **标题层次**：使用清晰的标题层次结构
2. **内容组织**：合理使用列表、引用等格式
3. **图文并茂**：适当添加图片和表格
4. **定期更新**：保持文档内容的时效性

### 知识管理
1. **分类规划**：提前规划好分类结构
2. **标签统一**：使用统一的标签命名规范
3. **版本控制**：重要文档要做好版本管理
4. **备份策略**：定期导出备份数据

这个知识库系统旨在提供最佳的知识管理体验，希望这份使用指南能帮助您快速上手并高效使用系统的各项功能。
