<template>
  <div class="knowledge-sidebar">
    <!-- 搜索框 -->
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索文档..."
        :prefix-icon="Search"
        clearable
        @input="handleSearch"
        @clear="handleClearSearch"
      />
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-button type="primary" :icon="Plus" @click="handleCreateDocument" block>
        新建文档
      </el-button>
    </div>

    <!-- 分类树 -->
    <div class="categories-section">
      <div class="section-header">
        <span class="section-title">知识分类</span>
        <el-button
          type="text"
          :icon="Plus"
          size="small"
          @click="handleCreateCategory"
        />
      </div>
      
      <el-tree
        ref="categoryTreeRef"
        :data="categoryTreeData"
        :props="treeProps"
        node-key="id"
        :expand-on-click-node="false"
        :highlight-current="true"
        @node-click="handleCategoryClick"
        class="category-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <el-icon v-if="data.icon" :color="data.color">
              <component :is="data.icon" />
            </el-icon>
            <span class="node-label">{{ node.label }}</span>
            <span class="node-count" v-if="getDocumentCount(data.id) > 0">
              {{ getDocumentCount(data.id) }}
            </span>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 标签云 -->
    <div class="tags-section">
      <div class="section-header">
        <span class="section-title">热门标签</span>
      </div>
      
      <div class="tags-container">
        <el-tag
          v-for="tag in popularTags"
          :key="tag.id"
          :color="tag.color"
          :effect="selectedTags.includes(tag.name) ? 'dark' : 'light'"
          class="tag-item"
          @click="handleTagClick(tag.name)"
        >
          {{ tag.name }} ({{ tag.count }})
        </el-tag>
      </div>
    </div>

    <!-- 最近文档 -->
    <div class="recent-section">
      <div class="section-header">
        <span class="section-title">最近编辑</span>
      </div>
      
      <div class="recent-list">
        <div
          v-for="doc in recentDocuments.slice(0, 5)"
          :key="doc.id"
          class="recent-item"
          @click="handleDocumentClick(doc)"
        >
          <div class="recent-title">{{ doc.title }}</div>
          <div class="recent-meta">
            <span class="recent-author">{{ doc.author }}</span>
            <span class="recent-time">{{ formatTime(doc.updatedAt) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search, Plus } from '@element-plus/icons-vue'
import { useKnowledgeStore } from '@/stores/counter'
import type { KnowledgeCategory, KnowledgeDocument } from '@/types/knowledge'

interface Emits {
  (e: 'create-document'): void
  (e: 'create-category'): void
  (e: 'category-selected', category: KnowledgeCategory | null): void
  (e: 'document-selected', document: KnowledgeDocument): void
  (e: 'search', query: string, tags: string[]): void
}

const emit = defineEmits<Emits>()

const knowledgeStore = useKnowledgeStore()

// 响应式数据
const searchQuery = ref('')
const selectedTags = ref<string[]>([])
const categoryTreeRef = ref()

// 计算属性
const categoryTreeData = computed(() => {
  return knowledgeStore.categories.map(category => ({
    ...category,
    label: category.name,
    children: category.children?.map(child => ({
      ...child,
      label: child.name
    }))
  }))
})

const popularTags = computed(() => {
  return knowledgeStore.tags.slice(0, 10)
})

const recentDocuments = computed(() => {
  return knowledgeStore.recentDocuments
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 方法
const getDocumentCount = (categoryId: string) => {
  const docs = knowledgeStore.documentsByCategory.get(categoryId)
  return docs ? docs.length : 0
}

const handleSearch = () => {
  emit('search', searchQuery.value, selectedTags.value)
}

const handleClearSearch = () => {
  searchQuery.value = ''
  selectedTags.value = []
  emit('search', '', [])
}

const handleCreateDocument = () => {
  emit('create-document')
}

const handleCreateCategory = () => {
  emit('create-category')
}

const handleCategoryClick = (data: KnowledgeCategory) => {
  knowledgeStore.setCurrentCategory(data)
  emit('category-selected', data)
}

const handleTagClick = (tagName: string) => {
  const index = selectedTags.value.indexOf(tagName)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagName)
  }
  emit('search', searchQuery.value, selectedTags.value)
}

const handleDocumentClick = (document: KnowledgeDocument) => {
  emit('document-selected', document)
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - new Date(date).getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    if (hours === 0) {
      const minutes = Math.floor(diff / (1000 * 60))
      return `${minutes}分钟前`
    }
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return new Date(date).toLocaleDateString()
  }
}

// 监听搜索查询变化
watch([searchQuery, selectedTags], () => {
  if (searchQuery.value || selectedTags.value.length > 0) {
    handleSearch()
  }
}, { deep: true })
</script>

<style scoped>
.knowledge-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color);
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.quick-actions {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.categories-section,
.tags-section,
.recent-section {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.category-tree {
  background: transparent;
}

:deep(.el-tree-node__content) {
  height: 36px;
  border-radius: 6px;
  margin-bottom: 2px;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  padding-right: 8px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-count {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
}

.tag-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recent-item {
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recent-item:hover {
  background-color: var(--el-fill-color-light);
}

.recent-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recent-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.recent-author,
.recent-time {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recent-author {
  max-width: 60px;
}
</style>
