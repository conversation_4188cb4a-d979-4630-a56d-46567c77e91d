<template>
  <div class="document-editor">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="handleCancel" size="small">
          取消
        </el-button>
        
        <div class="document-status">
          <el-tag :type="getStatusType(formData.status)" size="small">
            {{ getStatusText(formData.status) }}
          </el-tag>
        </div>
      </div>
      
      <div class="header-right">
        <el-button @click="handleSaveDraft" :loading="saving">
          保存草稿
        </el-button>
        <el-button type="primary" @click="handlePublish" :loading="saving">
          {{ formData.status === 'published' ? '更新' : '发布' }}
        </el-button>
      </div>
    </div>

    <!-- 编辑器内容 -->
    <div class="editor-content">
      <!-- 左侧编辑区 -->
      <div class="edit-area">
        <div class="document-form">
          <!-- 标题输入 -->
          <el-input
            v-model="formData.title"
            placeholder="请输入文档标题..."
            size="large"
            class="title-input"
            maxlength="100"
            show-word-limit
          />
          
          <!-- 富文本编辑器 -->
          <div class="content-editor">
            <RichEditor
              v-model="formData.content"
              placeholder="开始编写你的文档内容..."
              :editable="true"
            />
          </div>
        </div>
      </div>
      
      <!-- 右侧设置面板 -->
      <div class="settings-panel">
        <el-scrollbar>
          <div class="panel-content">
            <!-- 基本信息 -->
            <div class="setting-section">
              <h4 class="section-title">基本信息</h4>
              
              <el-form :model="formData" label-width="80px" size="small">
                <el-form-item label="分类">
                  <el-cascader
                    v-model="selectedCategoryPath"
                    :options="categoryOptions"
                    :props="cascaderProps"
                    placeholder="选择分类"
                    style="width: 100%"
                    @change="handleCategoryChange"
                  />
                </el-form-item>
                
                <el-form-item label="标签">
                  <el-select
                    v-model="formData.tags"
                    multiple
                    filterable
                    allow-create
                    placeholder="添加标签"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="tag in availableTags"
                      :key="tag.name"
                      :label="tag.name"
                      :value="tag.name"
                    />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="摘要">
                  <el-input
                    v-model="formData.summary"
                    type="textarea"
                    :rows="3"
                    placeholder="简要描述文档内容..."
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item label="状态">
                  <el-radio-group v-model="formData.status">
                    <el-radio value="draft">草稿</el-radio>
                    <el-radio value="published">发布</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="公开">
                  <el-switch
                    v-model="formData.isPublic"
                    active-text="公开"
                    inactive-text="私有"
                  />
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 文档统计 -->
            <div class="setting-section" v-if="document?.id">
              <h4 class="section-title">文档统计</h4>
              
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ document.viewCount }}</div>
                  <div class="stat-label">浏览次数</div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-value">{{ document.likeCount }}</div>
                  <div class="stat-label">点赞数</div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-value">{{ wordCount }}</div>
                  <div class="stat-label">字数</div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-value">{{ readingTime }}</div>
                  <div class="stat-label">阅读时间</div>
                </div>
              </div>
            </div>
            
            <!-- 版本信息 -->
            <div class="setting-section" v-if="document?.id">
              <h4 class="section-title">版本信息</h4>
              
              <div class="version-info">
                <div class="info-item">
                  <span class="info-label">创建时间:</span>
                  <span class="info-value">{{ formatTime(document.createdAt) }}</span>
                </div>
                
                <div class="info-item">
                  <span class="info-label">最后更新:</span>
                  <span class="info-value">{{ formatTime(document.updatedAt) }}</span>
                </div>
                
                <div class="info-item">
                  <span class="info-label">最后编辑:</span>
                  <span class="info-value">{{ document.lastEditedBy }}</span>
                </div>
              </div>
            </div>
            
            <!-- 快捷操作 -->
            <div class="setting-section">
              <h4 class="section-title">快捷操作</h4>
              
              <div class="quick-actions">
                <el-button size="small" :icon="View" @click="handlePreview">
                  预览
                </el-button>
                
                <el-button size="small" :icon="Download" @click="handleExport">
                  导出
                </el-button>
                
                <el-button size="small" :icon="Share" @click="handleShare">
                  分享
                </el-button>
                
                <el-button
                  size="small"
                  :icon="Delete"
                  type="danger"
                  @click="handleDelete"
                  v-if="document?.id"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  ArrowLeft,
  View,
  Download,
  Share,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useKnowledgeStore } from '@/stores/counter'
import RichEditor from './RichEditor.vue'
import type { KnowledgeDocument, KnowledgeCategory } from '@/types/knowledge'

interface Props {
  document?: KnowledgeDocument | null
  categories: KnowledgeCategory[]
}

interface Emits {
  (e: 'save', document: KnowledgeDocument): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  document: null,
  categories: () => []
})

const emit = defineEmits<Emits>()

const knowledgeStore = useKnowledgeStore()

// 响应式数据
const saving = ref(false)
const selectedCategoryPath = ref<string[]>([])

const formData = ref<KnowledgeDocument>({
  id: '',
  title: '',
  content: '',
  summary: '',
  categoryId: '',
  tags: [],
  author: '当前用户',
  status: 'draft',
  isPublic: false,
  viewCount: 0,
  likeCount: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  lastEditedBy: '当前用户'
})

// 计算属性
const categoryOptions = computed(() => {
  const buildOptions = (categories: KnowledgeCategory[]): any[] => {
    return categories.map(category => ({
      value: category.id,
      label: category.name,
      children: category.children ? buildOptions(category.children) : undefined
    }))
  }
  return buildOptions(props.categories)
})

const cascaderProps = {
  expandTrigger: 'hover' as const,
  emitPath: false
}

const availableTags = computed(() => {
  return knowledgeStore.tags
})

const wordCount = computed(() => {
  // 简单的字数统计
  const text = formData.value.content.replace(/<[^>]*>/g, '')
  return text.length
})

const readingTime = computed(() => {
  // 按照每分钟200字的阅读速度计算
  const minutes = Math.ceil(wordCount.value / 200)
  return `${minutes}分钟`
})

// 方法
const handleCancel = () => {
  if (hasUnsavedChanges()) {
    ElMessageBox.confirm(
      '有未保存的更改，确定要离开吗？',
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      emit('cancel')
    }).catch(() => {
      // 用户取消
    })
  } else {
    emit('cancel')
  }
}

const handleSaveDraft = async () => {
  formData.value.status = 'draft'
  await saveDocument()
}

const handlePublish = async () => {
  if (!formData.value.title.trim()) {
    ElMessage.warning('请输入文档标题')
    return
  }
  
  if (!formData.value.content.trim()) {
    ElMessage.warning('请输入文档内容')
    return
  }
  
  if (!formData.value.categoryId) {
    ElMessage.warning('请选择文档分类')
    return
  }
  
  formData.value.status = 'published'
  await saveDocument()
}

const saveDocument = async () => {
  saving.value = true
  
  try {
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const documentToSave = {
      ...formData.value,
      updatedAt: new Date(),
      lastEditedBy: '当前用户'
    }
    
    emit('save', documentToSave)
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const handleCategoryChange = (value: string) => {
  formData.value.categoryId = value
}

const handlePreview = () => {
  ElMessage.info('预览功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleShare = () => {
  ElMessage.info('分享功能开发中...')
}

const handleDelete = () => {
  ElMessageBox.confirm(
    '确定要删除这篇文档吗？此操作不可恢复。',
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.info('删除功能开发中...')
  }).catch(() => {
    // 用户取消
  })
}

const hasUnsavedChanges = () => {
  if (!props.document) {
    return formData.value.title || formData.value.content
  }
  
  return (
    formData.value.title !== props.document.title ||
    formData.value.content !== props.document.content ||
    formData.value.summary !== props.document.summary ||
    formData.value.categoryId !== props.document.categoryId ||
    JSON.stringify(formData.value.tags) !== JSON.stringify(props.document.tags) ||
    formData.value.status !== props.document.status ||
    formData.value.isPublic !== props.document.isPublic
  )
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

const formatTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 监听文档变化
watch(() => props.document, (newDoc) => {
  if (newDoc) {
    formData.value = { ...newDoc }
    
    // 设置分类路径
    const category = knowledgeStore.getCategoryById(newDoc.categoryId)
    if (category) {
      selectedCategoryPath.value = category.parentId 
        ? [category.parentId, category.id]
        : [category.id]
    }
  }
}, { immediate: true })

// 自动保存功能
let autoSaveTimer: NodeJS.Timeout | null = null

watch([() => formData.value.title, () => formData.value.content], () => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
  
  autoSaveTimer = setTimeout(() => {
    if (hasUnsavedChanges() && formData.value.title && formData.value.content) {
      handleSaveDraft()
    }
  }, 30000) // 30秒自动保存
})

onMounted(() => {
  // 如果是新建文档，设置默认分类
  if (!props.document && props.categories.length > 0) {
    formData.value.categoryId = props.categories[0].id
    selectedCategoryPath.value = [props.categories[0].id]
  }
})
</script>

<style scoped>
.document-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.edit-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.document-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow: hidden;
}

.title-input {
  margin-bottom: 20px;
}

:deep(.title-input .el-input__inner) {
  font-size: 24px;
  font-weight: 600;
  border: none;
  box-shadow: none;
  padding: 12px 0;
}

:deep(.title-input .el-input__inner:focus) {
  border-bottom: 2px solid var(--el-color-primary);
}

.content-editor {
  flex: 1;
  overflow: hidden;
}

.settings-panel {
  width: 320px;
  border-left: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color-page);
}

.panel-content {
  padding: 20px;
}

.setting-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.setting-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.info-label {
  color: var(--el-text-color-secondary);
}

.info-value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .editor-content {
    flex-direction: column;
  }
  
  .settings-panel {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--el-border-color-lighter);
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .editor-header {
    padding: 12px 16px;
  }
  
  .document-form {
    padding: 16px;
  }
  
  .panel-content {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style>
