# 知识库管理系统

一个基于 Vue 3 的现代化知识库管理前端应用，提供简单易用美观的知识管理体验。

## ✨ 功能特性

### 📚 文档管理
- **富文本编辑器**：基于 TipTap 的强大编辑器，支持 Markdown 语法
- **分类管理**：树形结构的文档分类，支持多级分类
- **标签系统**：灵活的标签管理，便于文档归类和检索
- **版本控制**：文档编辑历史和版本管理

### 🔍 搜索功能
- **全文搜索**：支持标题、内容、标签的全文检索
- **高级筛选**：按分类、状态、作者等条件筛选
- **智能推荐**：基于内容相似度的相关文档推荐

### 📊 数据统计
- **仪表板**：直观的数据统计和可视化
- **文档统计**：浏览量、点赞数、字数统计
- **使用分析**：最近编辑、热门文档等

### 🎨 用户体验
- **响应式设计**：完美适配桌面端和移动端
- **现代化UI**：基于 Element Plus 的美观界面
- **主题切换**：支持浅色/深色主题
- **快捷操作**：丰富的键盘快捷键支持

## 🛠️ 技术栈

- **前端框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **UI组件库**：Element Plus
- **富文本编辑器**：TipTap
- **路由管理**：Vue Router
- **状态管理**：Pinia
- **开发语言**：TypeScript
- **代码规范**：ESLint

## 📦 项目结构

```
src/
├── components/           # 通用组件
│   ├── RichEditor.vue   # 富文本编辑器
│   ├── KnowledgeSidebar.vue  # 侧边栏
│   ├── DocumentList.vue # 文档列表
│   ├── DocumentDetail.vue   # 文档详情
│   ├── DocumentEditor.vue   # 文档编辑器
│   └── KnowledgeDashboard.vue # 仪表板
├── views/               # 页面组件
│   └── KnowledgeBase.vue # 知识库主页面
├── stores/              # Pinia状态管理
│   └── counter.ts       # 知识库状态
├── types/               # TypeScript类型定义
│   └── knowledge.ts     # 知识库相关类型
├── router/              # 路由配置
└── assets/              # 静态资源
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```sh
npm install
```

### 启动开发服务器
```sh
npm run dev
```

### 构建生产版本
```sh
npm run build
```

### 代码检查
```sh
npm run lint
```

## 📱 功能演示

### 仪表板
- 数据统计卡片
- 最近编辑文档
- 热门文档排行
- 快速操作入口

### 文档编辑
- 所见即所得的富文本编辑
- 实时预览和自动保存
- 文档分类和标签管理
- 发布状态控制

### 文档浏览
- 清晰的文档结构展示
- 目录导航和锚点跳转
- 相关文档推荐
- 评论和互动功能

### 搜索和筛选
- 实时搜索建议
- 多维度筛选条件
- 搜索结果高亮
- 搜索历史记录

## 🎯 设计理念

本项目参考了市面上最优秀的知识库产品（如 Notion、语雀、Obsidian 等）的设计理念：

- **简洁直观**：清晰的信息架构和导航结构
- **高效编辑**：流畅的编辑体验和丰富的格式支持
- **智能组织**：灵活的分类和标签系统
- **协作友好**：便于分享和协作的功能设计

## 📄 许可证

MIT License
