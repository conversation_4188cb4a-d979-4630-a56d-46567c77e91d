import { createRouter, createWebHistory } from 'vue-router'
import TestPage from '../views/TestPage.vue'
import SimpleKnowledge from '../views/SimpleKnowledge.vue'
import KnowledgeBase from '../views/KnowledgeBase.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'basic-knowledge',
      component: () => import('../views/BasicKnowledge.vue'),
    },
    {
      path: '/test',
      name: 'test',
      component: TestPage,
    },
    {
      path: '/knowledge',
      name: 'knowledge-base',
      component: KnowledgeBase,
    },
  ],
})

export default router
