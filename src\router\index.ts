import { createRouter, createWebHistory } from 'vue-router'
import KnowledgeBase from '../views/KnowledgeBase.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'knowledge-base',
      component: KnowledgeBase,
    },
    {
      path: '/knowledge',
      name: 'knowledge',
      component: KnowledgeBase,
    },
  ],
})

export default router
