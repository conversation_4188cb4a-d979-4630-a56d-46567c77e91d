{"version": 3, "sources": ["../../tippy.js/src/constants.ts", "../../tippy.js/src/utils.ts", "../../tippy.js/src/dom-utils.ts", "../../tippy.js/src/bindGlobalEventListeners.ts", "../../tippy.js/src/browser.ts", "../../tippy.js/src/validation.ts", "../../tippy.js/src/props.ts", "../../tippy.js/src/template.ts", "../../tippy.js/src/createTippy.ts", "../../tippy.js/src/index.ts", "../../tippy.js/src/addons/createSingleton.ts", "../../tippy.js/src/addons/delegate.ts", "../../tippy.js/src/plugins/animateFill.ts", "../../tippy.js/src/plugins/followCursor.ts", "../../tippy.js/src/plugins/inlinePositioning.ts", "../../tippy.js/src/plugins/sticky.ts", "../../tippy.js/build/base.js", "../../@tiptap/extension-bubble-menu/src/bubble-menu-plugin.ts", "../../@tiptap/extension-bubble-menu/src/bubble-menu.ts", "../../@tiptap/extension-floating-menu/src/floating-menu-plugin.ts", "../../@tiptap/extension-floating-menu/src/floating-menu.ts", "../../@tiptap/vue-3/src/BubbleMenu.ts", "../../@tiptap/vue-3/src/Editor.ts", "../../@tiptap/vue-3/src/EditorContent.ts", "../../@tiptap/vue-3/src/FloatingMenu.ts", "../../@tiptap/vue-3/src/NodeViewContent.ts", "../../@tiptap/vue-3/src/NodeViewWrapper.ts", "../../@tiptap/vue-3/src/useEditor.ts", "../../@tiptap/vue-3/src/VueRenderer.ts", "../../@tiptap/vue-3/src/VueNodeViewRenderer.ts"], "sourcesContent": ["export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import tippy from '../src';\nimport {render} from '../src/template';\n\ntippy.setDefaultProps({render});\n\nexport {default, hideAll} from '../src';\nexport {default as createSingleton} from '../src/addons/createSingleton';\nexport {default as delegate} from '../src/addons/delegate';\nexport {default as animateFill} from '../src/plugins/animateFill';\nexport {default as followCursor} from '../src/plugins/followCursor';\nexport {default as inlinePositioning} from '../src/plugins/inlinePositioning';\nexport {default as sticky} from '../src/plugins/sticky';\nexport {ROUND_ARROW as roundArrow} from '../src/constants';\n", "import {\n  Editor, isNodeSelection, isTextSelection, posToDOMRect,\n} from '@tiptap/core'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface BubbleMenuPluginProps {\n  /**\n   * The plugin key.\n   * @type {PluginKey | string}\n   * @default 'bubbleMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy.js instance.\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * The delay in milliseconds before the menu should be updated.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 250\n   */\n  updateDelay?: number\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        element: HTMLElement\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n}\n\nexport type BubbleMenuViewProps = BubbleMenuPluginProps & {\n  view: EditorView\n}\n\nexport class BubbleMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  public updateDelay: number\n\n  private updateDebounceTimer: number | undefined\n\n  public shouldShow: Exclude<BubbleMenuPluginProps['shouldShow'], null> = ({\n    view,\n    state,\n    from,\n    to,\n  }) => {\n    const { doc, selection } = state\n    const { empty } = selection\n\n    // Sometime check for `empty` is not enough.\n    // Doubleclick an empty paragraph returns a node size of 2.\n    // So we check also for an empty text size.\n    const isEmptyTextBlock = !doc.textBetween(from, to).length && isTextSelection(state.selection)\n\n    // When clicking on a element inside the bubble menu the editor \"blur\" event\n    // is called and the bubble menu item is focussed. In this case we should\n    // consider the menu as part of the editor and keep showing the menu\n    const isChildOfMenu = this.element.contains(document.activeElement)\n\n    const hasEditorFocus = view.hasFocus() || isChildOfMenu\n\n    if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor,\n    element,\n    view,\n    tippyOptions = {},\n    updateDelay = 250,\n    shouldShow,\n  }: BubbleMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n    this.updateDelay = updateDelay\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.addEventListener('dragstart', this.dragstartHandler)\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  dragstartHandler = () => {\n    this.hide()\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'top',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const hasValidSelection = state.selection.from !== state.selection.to\n\n    if (this.updateDelay > 0 && hasValidSelection) {\n      this.handleDebouncedUpdate(view, oldState)\n      return\n    }\n\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  handleDebouncedUpdate = (view: EditorView, oldState?: EditorState) => {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    if (!selectionChanged && !docChanged) {\n      return\n    }\n\n    if (this.updateDebounceTimer) {\n      clearTimeout(this.updateDebounceTimer)\n    }\n\n    this.updateDebounceTimer = window.setTimeout(() => {\n      this.updateHandler(view, selectionChanged, docChanged, oldState)\n    }, this.updateDelay)\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { state, composing } = view\n    const { selection } = state\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    // support for CellSelections\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      element: this.element,\n      view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect\n        || (() => {\n          if (isNodeSelection(state.selection)) {\n            let node = view.nodeDOM(from) as HTMLElement\n\n            if (node) {\n              const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]')\n\n              if (nodeViewWrapper) {\n                node = nodeViewWrapper.firstChild as HTMLElement\n              }\n\n              if (node) {\n                return node.getBoundingClientRect()\n              }\n            }\n          }\n\n          return posToDOMRect(view, from, to)\n        }),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.removeEventListener('dragstart', this.dragstartHandler)\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const BubbleMenuPlugin = (options: BubbleMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new BubbleMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { BubbleMenuPlugin, BubbleMenuPluginProps } from './bubble-menu-plugin.js'\n\nexport type BubbleMenuOptions = Omit<BubbleMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nexport const BubbleMenu = Extension.create<BubbleMenuOptions>({\n  name: 'bubbleMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'bubbleMenu',\n      updateDelay: undefined,\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      BubbleMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        updateDelay: this.options.updateDelay,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "import {\n  Editor, getText, getTextSerializersFromSchema, posToDOMRect,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\nimport tippy, { Instance, Props } from 'tippy.js'\n\nexport interface FloatingMenuPluginProps {\n  /**\n   * The plugin key for the floating menu.\n   * @default 'floatingMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   * @default null\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The options for the tippy instance.\n   * @default {}\n   * @see https://atomiks.github.io/tippyjs/v6/all-props/\n   */\n  tippyOptions?: Partial<Props>\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   * @default null\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n      }) => boolean)\n    | null\n}\n\nexport type FloatingMenuViewProps = FloatingMenuPluginProps & {\n  /**\n   * The editor view.\n   */\n  view: EditorView\n}\n\nexport class FloatingMenuView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public tippy: Instance | undefined\n\n  public tippyOptions?: Partial<Props>\n\n  private getTextContent(node:ProseMirrorNode) {\n    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) })\n  }\n\n  public shouldShow: Exclude<FloatingMenuPluginProps['shouldShow'], null> = ({ view, state }) => {\n    const { selection } = state\n    const { $anchor, empty } = selection\n    const isRootDepth = $anchor.depth === 1\n\n    const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent)\n\n    if (\n      !view.hasFocus()\n      || !empty\n      || !isRootDepth\n      || !isEmptyTextBlock\n      || !this.editor.isEditable\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  constructor({\n    editor, element, view, tippyOptions = {}, shouldShow,\n  }: FloatingMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    this.tippyOptions = tippyOptions\n    // Detaches menu content from its current parent\n    this.element.remove()\n    this.element.style.visibility = 'visible'\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (\n      event?.relatedTarget === this.editor.view.dom\n    ) {\n      return\n    }\n\n    this.hide()\n  }\n\n  tippyBlurHandler = (event: FocusEvent) => {\n    this.blurHandler({ event })\n  }\n\n  createTooltip() {\n    const { element: editorElement } = this.editor.options\n    const editorIsAttached = !!editorElement.parentElement\n\n    if (this.tippy || !editorIsAttached) {\n      return\n    }\n\n    this.tippy = tippy(editorElement, {\n      duration: 0,\n      getReferenceClientRect: null,\n      content: this.element,\n      interactive: true,\n      trigger: 'manual',\n      placement: 'right',\n      hideOnClick: 'toggle',\n      ...this.tippyOptions,\n    })\n\n    // maybe we have to hide tippy on its own blur event as well\n    if (this.tippy.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).addEventListener('blur', this.tippyBlurHandler)\n    }\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const { doc, selection } = state\n    const { from, to } = selection\n    const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection)\n\n    if (isSame) {\n      return\n    }\n\n    this.createTooltip()\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      view,\n      state,\n      oldState,\n    })\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.tippy?.setProps({\n      getReferenceClientRect:\n        this.tippyOptions?.getReferenceClientRect || (() => posToDOMRect(view, from, to)),\n    })\n\n    this.show()\n  }\n\n  show() {\n    this.tippy?.show()\n  }\n\n  hide() {\n    this.tippy?.hide()\n  }\n\n  destroy() {\n    if (this.tippy?.popper.firstChild) {\n      (this.tippy.popper.firstChild as HTMLElement).removeEventListener(\n        'blur',\n        this.tippyBlurHandler,\n      )\n    }\n    this.tippy?.destroy()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n  }\n}\n\nexport const FloatingMenuPlugin = (options: FloatingMenuPluginProps) => {\n  return new Plugin({\n    key:\n      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new FloatingMenuView({ view, ...options }),\n  })\n}\n", "import { Extension } from '@tiptap/core'\n\nimport { FloatingMenuPlugin, FloatingMenuPluginProps } from './floating-menu-plugin.js'\n\nexport type FloatingMenuOptions = Omit<FloatingMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null,\n}\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nexport const FloatingMenu = Extension.create<FloatingMenuOptions>({\n  name: 'floatingMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      tippyOptions: {},\n      pluginKey: 'floatingMenu',\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      FloatingMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        tippyOptions: this.options.tippyOptions,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport {\n  defineComponent,\n  h,\n  onBeforeUnmount,\n  onMounted,\n  PropType,\n  ref,\n} from 'vue'\n\nexport const BubbleMenu = defineComponent({\n  name: 'BubbleMenu',\n\n  props: {\n    pluginKey: {\n      type: [String, Object] as PropType<BubbleMenuPluginProps['pluginKey']>,\n      default: 'bubbleMenu',\n    },\n\n    editor: {\n      type: Object as PropType<BubbleMenuPluginProps['editor']>,\n      required: true,\n    },\n\n    updateDelay: {\n      type: Number as PropType<BubbleMenuPluginProps['updateDelay']>,\n      default: undefined,\n    },\n\n    tippyOptions: {\n      type: Object as PropType<BubbleMenuPluginProps['tippyOptions']>,\n      default: () => ({}),\n    },\n\n    shouldShow: {\n      type: Function as PropType<Exclude<Required<BubbleMenuPluginProps>['shouldShow'], null>>,\n      default: null,\n    },\n  },\n\n  setup(props, { slots }) {\n    const root = ref<HTMLElement | null>(null)\n\n    onMounted(() => {\n      const {\n        updateDelay,\n        editor,\n        pluginKey,\n        shouldShow,\n        tippyOptions,\n      } = props\n\n      editor.registerPlugin(BubbleMenuPlugin({\n        updateDelay,\n        editor,\n        element: root.value as HTMLElement,\n        pluginKey,\n        shouldShow,\n        tippyOptions,\n      }))\n    })\n\n    onBeforeUnmount(() => {\n      const { pluginKey, editor } = props\n\n      editor.unregisterPlugin(pluginKey)\n    })\n\n    return () => h('div', { ref: root }, slots.default?.())\n  },\n})\n", "/* eslint-disable react-hooks/rules-of-hooks */\nimport { Editor as CoreEditor, EditorOptions } from '@tiptap/core'\nimport { Editor<PERSON><PERSON>, <PERSON>lugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport {\n  AppContext,\n  ComponentInternalInstance,\n  ComponentPublicInstance,\n  customRef,\n  markRaw,\n  Ref,\n} from 'vue'\n\nfunction useDebouncedRef<T>(value: T) {\n  return customRef<T>((track, trigger) => {\n    return {\n      get() {\n        track()\n        return value\n      },\n      set(newValue) {\n        // update state\n        value = newValue\n\n        // update view as soon as possible\n        requestAnimationFrame(() => {\n          requestAnimationFrame(() => {\n            trigger()\n          })\n        })\n      },\n    }\n  })\n}\n\nexport type ContentComponent = ComponentInternalInstance & {\n  ctx: ComponentPublicInstance\n}\n\nexport class Editor extends CoreEditor {\n  private reactiveState: Ref<EditorState>\n\n  private reactiveExtensionStorage: Ref<Record<string, any>>\n\n  public contentComponent: ContentComponent | null = null\n\n  public appContext: AppContext | null = null\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super(options)\n\n    this.reactiveState = useDebouncedRef(this.view.state)\n    this.reactiveExtensionStorage = useDebouncedRef(this.extensionStorage)\n\n    this.on('beforeTransaction', ({ nextState }) => {\n      this.reactiveState.value = nextState\n      this.reactiveExtensionStorage.value = this.extensionStorage\n    })\n\n    return markRaw(this) // eslint-disable-line\n  }\n\n  get state() {\n    return this.reactiveState ? this.reactiveState.value : this.view.state\n  }\n\n  get storage() {\n    return this.reactiveExtensionStorage ? this.reactiveExtensionStorage.value : super.storage\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const nextState = super.registerPlugin(plugin, handlePlugins)\n\n    if (this.reactiveState) {\n      this.reactiveState.value = nextState\n    }\n\n    return nextState\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   */\n  public unregisterPlugin(nameOrPluginKey: string | PluginKey): EditorState | undefined {\n    const nextState = super.unregisterPlugin(nameOrPluginKey)\n\n    if (this.reactiveState && nextState) {\n      this.reactiveState.value = nextState\n    }\n\n    return nextState\n  }\n}\n", "import {\n  defineComponent,\n  getCurrentInstance,\n  h,\n  nextTick,\n  onBeforeUnmount,\n  PropType,\n  Ref,\n  ref,\n  unref,\n  watchEffect,\n} from 'vue'\n\nimport { Editor } from './Editor.js'\n\nexport const EditorContent = defineComponent({\n  name: 'EditorContent',\n\n  props: {\n    editor: {\n      default: null,\n      type: Object as PropType<Editor>,\n    },\n  },\n\n  setup(props) {\n    const rootEl: Ref<Element | undefined> = ref()\n    const instance = getCurrentInstance()\n\n    watchEffect(() => {\n      const editor = props.editor\n\n      if (editor && editor.options.element && rootEl.value) {\n        nextTick(() => {\n          if (!rootEl.value || !editor.options.element.firstChild) {\n            return\n          }\n\n          const element = unref(rootEl.value)\n\n          rootEl.value.append(...editor.options.element.childNodes)\n\n          // @ts-ignore\n          editor.contentComponent = instance.ctx._\n\n          if (instance) {\n            editor.appContext = {\n              ...instance.appContext,\n              // Vue internally uses prototype chain to forward/shadow injects across the entire component chain\n              // so don't use object spread operator or 'Object.assign' and just set `provides` as is on editor's appContext\n              // @ts-expect-error forward instance's 'provides' into appContext\n              provides: instance.provides,\n            }\n          }\n\n          editor.setOptions({\n            element,\n          })\n\n          editor.createNodeViews()\n        })\n      }\n    })\n\n    onBeforeUnmount(() => {\n      const editor = props.editor\n\n      if (!editor) {\n        return\n      }\n\n      editor.contentComponent = null\n      editor.appContext = null\n    })\n\n    return { rootEl }\n  },\n\n  render() {\n    return h(\n      'div',\n      {\n        ref: (el: any) => { this.rootEl = el },\n      },\n    )\n  },\n})\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport {\n  defineComponent,\n  h,\n  onBeforeUnmount,\n  onMounted,\n  PropType,\n  ref,\n} from 'vue'\n\nexport const FloatingMenu = defineComponent({\n  name: 'FloatingMenu',\n\n  props: {\n    pluginKey: {\n      // TODO: TypeScript breaks :(\n      // type: [String, Object as PropType<Exclude<FloatingMenuPluginProps['pluginKey'], string>>],\n      type: null,\n      default: 'floatingMenu',\n    },\n\n    editor: {\n      type: Object as PropType<FloatingMenuPluginProps['editor']>,\n      required: true,\n    },\n\n    tippyOptions: {\n      type: Object as PropType<FloatingMenuPluginProps['tippyOptions']>,\n      default: () => ({}),\n    },\n\n    shouldShow: {\n      type: Function as PropType<Exclude<Required<FloatingMenuPluginProps>['shouldShow'], null>>,\n      default: null,\n    },\n  },\n\n  setup(props, { slots }) {\n    const root = ref<HTMLElement | null>(null)\n\n    onMounted(() => {\n      const {\n        pluginKey,\n        editor,\n        tippyOptions,\n        shouldShow,\n      } = props\n\n      editor.registerPlugin(FloatingMenuPlugin({\n        pluginKey,\n        editor,\n        element: root.value as HTMLElement,\n        tippyOptions,\n        shouldShow,\n      }))\n    })\n\n    onBeforeUnmount(() => {\n      const { pluginKey, editor } = props\n\n      editor.unregisterPlugin(pluginKey)\n    })\n\n    return () => h('div', { ref: root }, slots.default?.())\n  },\n})\n", "import { defineComponent, h } from 'vue'\n\nexport const NodeViewContent = defineComponent({\n  name: 'NodeViewContent',\n\n  props: {\n    as: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  render() {\n    return h(this.as, {\n      style: {\n        whiteSpace: 'pre-wrap',\n      },\n      'data-node-view-content': '',\n    })\n  },\n})\n", "import { defineComponent, h } from 'vue'\n\nexport const NodeViewWrapper = defineComponent({\n  name: 'NodeViewWrapper',\n\n  props: {\n    as: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  inject: ['onDragStart', 'decorationClasses'],\n\n  render() {\n    return h(\n      this.as,\n      {\n        // @ts-ignore\n        class: this.decorationClasses,\n        style: {\n          whiteSpace: 'normal',\n        },\n        'data-node-view-wrapper': '',\n        // @ts-ignore (https://github.com/vuejs/vue-next/issues/3031)\n        onDragstart: this.onDragStart,\n      },\n      this.$slots.default?.(),\n    )\n  },\n})\n", "import { EditorOptions } from '@tiptap/core'\nimport { onBeforeUnmount, onMounted, shallowRef } from 'vue'\n\nimport { Editor } from './Editor.js'\n\nexport const useEditor = (options: Partial<EditorOptions> = {}) => {\n  const editor = shallowRef<Editor>()\n\n  onMounted(() => {\n    editor.value = new Editor(options)\n  })\n\n  onBeforeUnmount(() => {\n    // Cloning root node (and its children) to avoid content being lost by destroy\n    const nodes = editor.value?.options.element\n    const newEl = nodes?.cloneNode(true) as HTMLElement\n\n    nodes?.parentNode?.replaceChild(newEl, nodes)\n\n    editor.value?.destroy()\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport {\n  Component, DefineComponent, h, markRaw, reactive, render,\n} from 'vue'\n\nimport { Editor as ExtendedEditor } from './Editor.js'\n\nexport interface VueRendererOptions {\n  editor: Editor;\n  props?: Record<string, any>;\n}\n\ntype ExtendedVNode = ReturnType<typeof h> | null;\n\ninterface RenderedComponent {\n  vNode: ExtendedVNode;\n  destroy: () => void;\n  el: Element | null;\n}\n\n/**\n * This class is used to render Vue components inside the editor.\n */\nexport class VueRenderer {\n  renderedComponent!: RenderedComponent\n\n  editor: ExtendedEditor\n\n  component: Component\n\n  el: Element | null\n\n  props: Record<string, any>\n\n  constructor(component: Component, { props = {}, editor }: VueRendererOptions) {\n    this.editor = editor as ExtendedEditor\n    this.component = markRaw(component)\n    this.el = document.createElement('div')\n    this.props = reactive(props)\n    this.renderedComponent = this.renderComponent()\n  }\n\n  get element(): Element | null {\n    return this.renderedComponent.el\n  }\n\n  get ref(): any {\n    // Composition API\n    if (this.renderedComponent.vNode?.component?.exposed) {\n      return this.renderedComponent.vNode.component.exposed\n    }\n    // Option API\n    return this.renderedComponent.vNode?.component?.proxy\n  }\n\n  renderComponent() {\n    let vNode: ExtendedVNode = h(this.component as DefineComponent, this.props)\n\n    if (this.editor.appContext) {\n      vNode.appContext = this.editor.appContext\n    }\n    if (typeof document !== 'undefined' && this.el) {\n      render(vNode, this.el)\n    }\n\n    const destroy = () => {\n      if (this.el) {\n        render(null, this.el)\n      }\n      this.el = null\n      vNode = null\n    }\n\n    return { vNode, destroy, el: this.el ? this.el.firstElementChild : null }\n  }\n\n  updateProps(props: Record<string, any> = {}): void {\n    Object.entries(props).forEach(([key, value]) => {\n      this.props[key] = value\n    })\n    this.renderComponent()\n  }\n\n  destroy(): void {\n    this.renderedComponent.destroy()\n  }\n}\n", "/* eslint-disable no-underscore-dangle */\nimport {\n  DecorationWithType,\n  NodeView,\n  NodeViewProps,\n  NodeViewRenderer,\n  NodeViewRendererOptions,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport {\n  Component, defineComponent, PropType, provide, Ref, ref,\n} from 'vue'\n\nimport { Editor } from './Editor.js'\nimport { VueRenderer } from './VueRenderer.js'\n\nexport const nodeViewProps = {\n  editor: {\n    type: Object as PropType<NodeViewProps['editor']>,\n    required: true as const,\n  },\n  node: {\n    type: Object as PropType<NodeViewProps['node']>,\n    required: true as const,\n  },\n  decorations: {\n    type: Object as PropType<NodeViewProps['decorations']>,\n    required: true as const,\n  },\n  selected: {\n    type: Boolean as PropType<NodeViewProps['selected']>,\n    required: true as const,\n  },\n  extension: {\n    type: Object as PropType<NodeViewProps['extension']>,\n    required: true as const,\n  },\n  getPos: {\n    type: Function as PropType<NodeViewProps['getPos']>,\n    required: true as const,\n  },\n  updateAttributes: {\n    type: Function as PropType<NodeViewProps['updateAttributes']>,\n    required: true as const,\n  },\n  deleteNode: {\n    type: Function as PropType<NodeViewProps['deleteNode']>,\n    required: true as const,\n  },\n  view: {\n    type: Object as PropType<NodeViewProps['view']>,\n    required: true as const,\n  },\n  innerDecorations: {\n    type: Object as PropType<NodeViewProps['innerDecorations']>,\n    required: true as const,\n  },\n  HTMLAttributes: {\n    type: Object as PropType<NodeViewProps['HTMLAttributes']>,\n    required: true as const,\n  },\n}\n\nexport interface VueNodeViewRendererOptions extends NodeViewRendererOptions {\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n}\n\nclass VueNodeView extends NodeView<Component, Editor, VueNodeViewRendererOptions> {\n  renderer!: VueRenderer\n\n  decorationClasses!: Ref<string>\n\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n    } satisfies NodeViewProps\n\n    const onDragStart = this.onDragStart.bind(this)\n\n    this.decorationClasses = ref(this.getDecorationClasses())\n\n    const extendedComponent = defineComponent({\n      extends: { ...this.component },\n      props: Object.keys(props),\n      template: (this.component as any).template,\n      setup: reactiveProps => {\n        provide('onDragStart', onDragStart)\n        provide('decorationClasses', this.decorationClasses)\n\n        return (this.component as any).setup?.(reactiveProps, {\n          expose: () => undefined,\n        })\n      },\n      // add support for scoped styles\n      // @ts-ignore\n      // eslint-disable-next-line\n      __scopeId: this.component.__scopeId,\n      // add support for CSS Modules\n      // @ts-ignore\n      // eslint-disable-next-line\n      __cssModules: this.component.__cssModules,\n      // add support for vue devtools\n      // @ts-ignore\n      // eslint-disable-next-line\n      __name: this.component.__name,\n      // @ts-ignore\n      // eslint-disable-next-line\n      __file: this.component.__file,\n    })\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n\n    this.renderer = new VueRenderer(extendedComponent, {\n      editor: this.editor,\n      props,\n    })\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (!this.renderer.element || !this.renderer.element.hasAttribute('data-node-view-wrapper')) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.dom.querySelector('[data-node-view-content]') as HTMLElement | null\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: ProseMirrorNode,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.decorationClasses.value = this.getDecorationClasses()\n      this.renderer.updateProps(props)\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    if (this.renderer.element) {\n      this.renderer.element.classList.add('ProseMirror-selectednode')\n    }\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    if (this.renderer.element) {\n      this.renderer.element.classList.remove('ProseMirror-selectednode')\n    }\n  }\n\n  getDecorationClasses() {\n    return (\n      this.decorations\n        // @ts-ignore\n        .map(item => item.type.attrs.class)\n        .flat()\n        .join(' ')\n    )\n  }\n\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n  }\n}\n\nexport function VueNodeViewRenderer(\n  component: Component<NodeViewProps>,\n  options?: Partial<VueNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as Editor).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n    // check for class-component and normalize if neccessary\n    const normalizedComponent = typeof component === 'function' && '__vccOpts' in component\n      ? (component.__vccOpts as Component)\n      : component\n\n    return new VueNodeView(normalizedComponent, props, options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,IAAMA,YAAS;AACf,IAAMC,gBAAa;AACnB,IAAMC,iBAAc;AACpB,IAAMC,cAAW;AACjB,IAAMC,kBAAe;AAErB,IAAMC,gBAAgB;EAACC,SAAS;EAAMC,SAAS;AAAzB;AAEtB,IAAMC,0BAA0B,SAA1BA,2BAA0B;AAAA,SAAMC,SAASC;AAAf;ACThC,SAASC,eACdC,KACAC,KACS;AACT,SAAO,CAAA,EAAGF,eAAeG,KAAKF,KAAKC,GAA5B;AACR;AAEM,SAASE,wBACdC,OACAC,QACAC,cACG;AACH,MAAIC,MAAMC,QAAQJ,KAAd,GAAsB;AACxB,QAAMK,IAAIL,MAAMC,MAAD;AACf,WAAOI,KAAK,OACRF,MAAMC,QAAQF,YAAd,IACEA,aAAaD,MAAD,IACZC,eACFG;EACL;AAED,SAAOL;AACR;AAEM,SAASM,OAAON,OAAYO,MAAuB;AACxD,MAAMC,MAAM,CAAA,EAAGC,SAASX,KAAKE,KAAjB;AACZ,SAAOQ,IAAIE,QAAQ,SAAZ,MAA2B,KAAKF,IAAIE,QAAWH,OAAf,GAAA,IAA0B;AAClE;AAEM,SAASI,uBAAuBX,OAAYY,MAAkB;AACnE,SAAO,OAAOZ,UAAU,aAAaA,MAAK,MAAL,QAASY,IAAT,IAAiBZ;AACvD;AAEM,SAASa,SACdC,IACAC,IACkB;AAElB,MAAIA,OAAO,GAAG;AACZ,WAAOD;EACR;AAED,MAAIE;AAEJ,SAAO,SAACC,KAAc;AACpBC,iBAAaF,OAAD;AACZA,cAAUG,WAAW,WAAM;AACzBL,SAAGG,GAAD;IACH,GAAEF,EAFiB;EAGrB;AACF;AAEM,SAASK,iBAAoBxB,KAAQyB,MAA4B;AACtE,MAAMC,QAAK,OAAA,OAAA,CAAA,GAAO1B,GAAP;AACXyB,OAAKE,QAAQ,SAAC1B,KAAQ;AACpB,WAAQyB,MAAczB,GAAf;EACR,CAFD;AAGA,SAAOyB;AACR;AAEM,SAASE,cAAcxB,OAAyB;AACrD,SAAOA,MAAMyB,MAAM,KAAZ,EAAmBC,OAAOC,OAA1B;AACR;AAEM,SAASC,iBAAoB5B,OAAqB;AACvD,SAAQ,CAAA,EAAW6B,OAAO7B,KAAnB;AACR;AAEM,SAAS8B,aAAgBC,KAAU/B,OAAgB;AACxD,MAAI+B,IAAIrB,QAAQV,KAAZ,MAAuB,IAAI;AAC7B+B,QAAIC,KAAKhC,KAAT;EACD;AACF;AAMM,SAASiC,OAAUF,KAAe;AACvC,SAAOA,IAAIL,OAAO,SAACQ,MAAMjC,QAAP;AAAA,WAAiB8B,IAAIrB,QAAQwB,IAAZ,MAAsBjC;EAAvC,CAAX;AACR;AAMM,SAASkC,iBAAiBC,WAAqC;AACpE,SAAOA,UAAUX,MAAM,GAAhB,EAAqB,CAArB;AACR;AAEM,SAASY,UAAUrC,OAA8B;AACtD,SAAO,CAAA,EAAGsC,MAAMxC,KAAKE,KAAd;AACR;AAEM,SAASuC,qBACd3C,KACkC;AAClC,SAAO4C,OAAOnB,KAAKzB,GAAZ,EAAiB6C,OAAO,SAACC,KAAK7C,KAAQ;AAC3C,QAAID,IAAIC,GAAD,MAAU8C,QAAW;AACzBD,UAAY7C,GAAb,IAAoBD,IAAIC,GAAD;IACxB;AAED,WAAO6C;EACR,GAAE,CAAA,CANI;AAOR;ACtGM,SAASE,MAAsB;AACpC,SAAOnD,SAASoD,cAAc,KAAvB;AACR;AAEM,SAASC,UAAU9C,OAAqD;AAC7E,SAAO,CAAC,WAAW,UAAZ,EAAwB+C,KAAK,SAACxC,MAAD;AAAA,WAAUD,OAAON,OAAOO,IAAR;EAAhB,CAA7B;AACR;AAEM,SAASyC,WAAWhD,OAAmC;AAC5D,SAAOM,OAAON,OAAO,UAAR;AACd;AAEM,SAASiD,aAAajD,OAAqC;AAChE,SAAOM,OAAON,OAAO,YAAR;AACd;AAEM,SAASkD,mBAAmBlD,OAAuC;AACxE,SAAO,CAAC,EAAEA,SAASA,MAAMmD,UAAUnD,MAAMmD,OAAOC,cAAcpD;AAC/D;AAEM,SAASqD,mBAAmBrD,OAA2B;AAC5D,MAAI8C,UAAU9C,KAAD,GAAS;AACpB,WAAO,CAACA,KAAD;EACR;AAED,MAAIgD,WAAWhD,KAAD,GAAS;AACrB,WAAOqC,UAAUrC,KAAD;EACjB;AAED,MAAIG,MAAMC,QAAQJ,KAAd,GAAsB;AACxB,WAAOA;EACR;AAED,SAAOqC,UAAU5C,SAAS6D,iBAAiBtD,KAA1B,CAAD;AACjB;AAEM,SAASuD,sBACdC,KACAxD,OACM;AACNwD,MAAIjC,QAAQ,SAACkC,IAAO;AAClB,QAAIA,IAAI;AACNA,SAAGC,MAAMC,qBAAwB3D,QAAjC;IACD;EACF,CAJD;AAKD;AAEM,SAAS4D,mBACdJ,KACAK,OACM;AACNL,MAAIjC,QAAQ,SAACkC,IAAO;AAClB,QAAIA,IAAI;AACNA,SAAGK,aAAa,cAAcD,KAA9B;IACD;EACF,CAJD;AAKD;AAEM,SAASE,iBACdC,mBACU;AAAA,MAAA;AACV,MAAA,oBAAkBpC,iBAAiBoC,iBAAD,GAA3BC,UAAP,kBAAA,CAAA;AAGA,SAAOA,WAAO,SAAP,wBAAAA,QAASC,kBAAT,QAAA,sBAAwBxE,OAAOuE,QAAQC,gBAAgBzE;AAC/D;AAEM,SAAS0E,iCACdC,gBACAC,OACS;AACT,MAAOC,UAAoBD,MAApBC,SAASC,UAAWF,MAAXE;AAEhB,SAAOH,eAAeI,MAAM,SAAA,MAAsC;AAAA,QAApCC,aAAoC,KAApCA,YAAYC,cAAwB,KAAxBA,aAAaC,QAAW,KAAXA;AACrD,QAAOC,oBAAqBD,MAArBC;AACP,QAAMC,gBAAgB1C,iBAAiBuC,YAAYtC,SAAb;AACtC,QAAM0C,aAAaJ,YAAYK,cAAcC;AAE7C,QAAI,CAACF,YAAY;AACf,aAAO;IACR;AAED,QAAMG,cAAcJ,kBAAkB,WAAWC,WAAWI,IAAKC,IAAI;AACrE,QAAMC,iBAAiBP,kBAAkB,QAAQC,WAAWO,OAAQF,IAAI;AACxE,QAAMG,eAAeT,kBAAkB,UAAUC,WAAWS,KAAMC,IAAI;AACtE,QAAMC,gBAAgBZ,kBAAkB,SAASC,WAAWY,MAAOF,IAAI;AAEvE,QAAMG,aACJlB,WAAWS,MAAMX,UAAUU,cAAcL;AAC3C,QAAMgB,gBACJrB,UAAUE,WAAWY,SAASD,iBAAiBR;AACjD,QAAMiB,cACJpB,WAAWc,OAAOjB,UAAUgB,eAAeV;AAC7C,QAAMkB,eACJxB,UAAUG,WAAWiB,QAAQD,gBAAgBb;AAE/C,WAAOe,cAAcC,iBAAiBC,eAAeC;EACtD,CAxBM;AAyBR;AAEM,SAASC,4BACdC,KACAC,QACAC,UACM;AACN,MAAMC,SAAYF,SAAN;AAMZ,GAAC,iBAAiB,qBAAlB,EAAyC1E,QAAQ,SAAC8C,OAAU;AAC1D2B,QAAIG,MAAD,EAAS9B,OAAO6B,QAAnB;EACD,CAFD;AAGD;AAMM,SAASE,eAAeC,QAAiBC,OAAyB;AACvE,MAAIC,SAASD;AACb,SAAOC,QAAQ;AAAA,QAAA;AACb,QAAIF,OAAOG,SAASD,MAAhB,GAAyB;AAC3B,aAAO;IACR;AACDA,aAAUA,OAAOE,eAAX,OAAA,UAAA,sBAAIF,OAAOE,YAAP,MAAJ,OAAA,SAAG,oBAAiCC;EAC3C;AACD,SAAO;AACR;AClIM,IAAMC,eAAe;EAACC,SAAS;AAAV;AAC5B,IAAIC,oBAAoB;AAQjB,SAASC,uBAA6B;AAC3C,MAAIH,aAAaC,SAAS;AACxB;EACD;AAEDD,eAAaC,UAAU;AAEvB,MAAIG,OAAOC,aAAa;AACtBvH,aAASwH,iBAAiB,aAAaC,mBAAvC;EACD;AACF;AAOM,SAASA,sBAA4B;AAC1C,MAAMC,MAAMH,YAAYG,IAAZ;AAEZ,MAAIA,MAAMN,oBAAoB,IAAI;AAChCF,iBAAaC,UAAU;AAEvBnH,aAAS2H,oBAAoB,aAAaF,mBAA1C;EACD;AAEDL,sBAAoBM;AACrB;AAQM,SAASE,eAAqB;AACnC,MAAMC,gBAAgB7H,SAAS6H;AAE/B,MAAIpE,mBAAmBoE,aAAD,GAAiB;AACrC,QAAMC,WAAWD,cAAcnE;AAE/B,QAAImE,cAAcE,QAAQ,CAACD,SAAS1D,MAAM4D,WAAW;AACnDH,oBAAcE,KAAd;IACD;EACF;AACF;AAEc,SAASE,2BAAiC;AACvDjI,WAASwH,iBAAiB,cAAcH,sBAAsBzH,aAA9D;AACA0H,SAAOE,iBAAiB,QAAQI,YAAhC;AACD;AC9DM,IAAMM,YACX,OAAOZ,WAAW,eAAe,OAAOtH,aAAa;AAEhD,IAAMmI,SAASD;;EAElB,CAAC,CAACZ,OAAOc;IACT;ACJG,SAASC,wBAAwB3B,QAAwB;AAC9D,MAAM4B,MAAM5B,WAAW,YAAY,eAAe;AAElD,SAAO,CACFA,SADE,uBACyB4B,MADzB,2CAEL,oCAFK,EAGLC,KAAK,GAHA;AAIR;AAEM,SAASC,MAAMjI,OAAuB;AAC3C,MAAMkI,gBAAgB;AACtB,MAAMC,sBAAsB;AAE5B,SAAOnI,MACJoI,QAAQF,eAAe,GADnB,EAEJE,QAAQD,qBAAqB,EAFzB,EAGJE,KAHI;AAIR;AAED,SAASC,cAAcC,SAAyB;AAC9C,SAAON,MAAK,2BAGRA,MAAMM,OAAD,IAHG,uFAAA;AAOb;AAEM,SAASC,oBAAoBD,SAA2B;AAC7D,SAAO;IACLD,cAAcC,OAAD;;IAEb;;IAEA;;IAEA;EAPK;AASR;AAGD,IAAIE;AACJ,IAAA,MAAa;AACXC,uBAAoB;AACrB;AAEM,SAASA,uBAA6B;AAC3CD,oBAAkB,oBAAIE,IAAJ;AACnB;AAEM,SAASC,SAASC,WAAoBN,SAAuB;AAClE,MAAIM,aAAa,CAACJ,gBAAgBK,IAAIP,OAApB,GAA8B;AAAA,QAAA;AAC9CE,oBAAgBM,IAAIR,OAApB;AACA,KAAA,WAAAS,SAAQC,KAAR,MAAA,UAAgBT,oBAAoBD,OAAD,CAAnC;EACD;AACF;AAEM,SAASW,UAAUL,WAAoBN,SAAuB;AACnE,MAAIM,aAAa,CAACJ,gBAAgBK,IAAIP,OAApB,GAA8B;AAAA,QAAA;AAC9CE,oBAAgBM,IAAIR,OAApB;AACA,KAAA,YAAAS,SAAQG,MAAR,MAAA,WAAiBX,oBAAoBD,OAAD,CAApC;EACD;AACF;AAEM,SAASa,gBAAgBC,SAAwB;AACtD,MAAMC,oBAAoB,CAACD;AAC3B,MAAME,qBACJ/G,OAAOgH,UAAU/I,SAASX,KAAKuJ,OAA/B,MAA4C,qBAC5C,CAAEA,QAAgBpC;AAEpBiC,YACEI,mBACA,CACE,sBACA,MAAMG,OAAOJ,OAAD,IAAY,KACxB,sEACA,yBAJF,EAKErB,KAAK,GALP,CAFO;AAUTkB,YACEK,oBACA,CACE,2EACA,oEAFF,EAGEvB,KAAK,GAHP,CAFO;AAOV;ACjFD,IAAM0B,cAAc;EAClBC,aAAa;EACbC,cAAc;EACdC,mBAAmB;EACnBC,QAAQ;AAJU;AAOpB,IAAMC,cAAc;EAClBC,WAAW;EACXC,WAAW;EACXC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,OAAO;EACPC,QAAQ;AATU;AAYb,IAAMC,eAA0B,OAAA,OAAA;EACrCC,UAAUlL;EACVmL,MAAM;IACJR,SAAS;IACTS,UAAU;EAFN;EAINC,OAAO;EACPC,UAAU,CAAC,KAAK,GAAN;EACVC,wBAAwB;EACxBC,aAAa;EACbC,kBAAkB;EAClBC,aAAa;EACbtG,mBAAmB;EACnBuG,qBAAqB;EACrBC,gBAAgB;EAChBpG,QAAQ,CAAC,GAAG,EAAJ;EACRqG,eAhBqC,SAAA,gBAgBrB;EAAA;EAChBC,gBAjBqC,SAAA,iBAiBpB;EAAA;EACjBC,UAlBqC,SAAA,WAkB1B;EAAA;EACXC,WAnBqC,SAAA,YAmBzB;EAAA;EACZC,UApBqC,SAAA,WAoB1B;EAAA;EACXC,QArBqC,SAAA,SAqB5B;EAAA;EACTC,SAtBqC,SAAA,UAsB3B;EAAA;EACVC,QAvBqC,SAAA,SAuB5B;EAAA;EACTC,SAxBqC,SAAA,UAwB3B;EAAA;EACVC,WAzBqC,SAAA,YAyBzB;EAAA;EACZC,aA1BqC,SAAA,cA0BvB;EAAA;EACdC,gBA3BqC,SAAA,iBA2BpB;EAAA;EACjB5J,WAAW;EACX6J,SAAS,CAAA;EACTC,eAAe,CAAA;EACfC,QAAQ;EACRC,cAAc;EACdC,OAAO;EACPC,SAAS;EACTC,eAAe;AAnCsB,GAoClC7C,aACAK,WArCkC;AAwCvC,IAAMyC,cAAchK,OAAOnB,KAAKoJ,YAAZ;AAEb,IAAMgC,kBAA4C,SAA5CA,iBAA6CC,cAAiB;AAEzE,MAAA,MAAa;AACXC,kBAAcD,cAAc,CAAA,CAAf;EACd;AAED,MAAMrL,OAAOmB,OAAOnB,KAAKqL,YAAZ;AACbrL,OAAKE,QAAQ,SAAC1B,KAAQ;AACnB4K,iBAAqB5K,GAAtB,IAA6B6M,aAAa7M,GAAD;EAC1C,CAFD;AAGD;AAEM,SAAS+M,uBACdC,aACgB;AAChB,MAAMZ,UAAUY,YAAYZ,WAAW,CAAA;AACvC,MAAMvC,eAAcuC,QAAQxJ,OAAgC,SAACC,KAAKoK,QAAW;AAC3E,QAAOC,OAAsBD,OAAtBC,MAAM7M,eAAgB4M,OAAhB5M;AAEb,QAAI6M,MAAM;AAAA,UAAA;AACRrK,UAAIqK,IAAD,IACDF,YAAYE,IAAD,MAAWpK,SAClBkK,YAAYE,IAAD,KADf,QAEKtC,aAAqBsC,IAAtB,MAFJ,OAAA,QAEmC7M;IACtC;AAED,WAAOwC;EACR,GAAE,CAAA,CAXiB;AAapB,SAAA,OAAA,OAAA,CAAA,GACKmK,aACAnD,YAFL;AAID;AAEM,SAASsD,sBACd5J,WACA6I,SACyB;AACzB,MAAMgB,WAAWhB,UACbzJ,OAAOnB,KAAKuL,uBAAsB,OAAA,OAAA,CAAA,GAAKnC,cAAL;IAAmBwB;EAAnB,CAAA,CAAA,CAAlC,IACAO;AAEJ,MAAM7H,QAAQsI,SAASxK,OACrB,SAACC,KAA+C7C,KAAQ;AACtD,QAAMqN,iBACJ9J,UAAU+J,aAAV,gBAAqCtN,GAArC,KAA+C,IAC/CwI,KAFoB;AAItB,QAAI,CAAC6E,eAAe;AAClB,aAAOxK;IACR;AAED,QAAI7C,QAAQ,WAAW;AACrB6C,UAAI7C,GAAD,IAAQqN;IACZ,OAAM;AACL,UAAI;AACFxK,YAAI7C,GAAD,IAAQuN,KAAKC,MAAMH,aAAX;MACZ,SAAQI,GAAG;AACV5K,YAAI7C,GAAD,IAAQqN;MACZ;IACF;AAED,WAAOxK;EACR,GACD,CAAA,CAtBY;AAyBd,SAAOiC;AACR;AAEM,SAAS4I,cACdnK,WACAuB,OACO;AACP,MAAM6I,MAAG,OAAA,OAAA,CAAA,GACJ7I,OADI;IAEPwF,SAASxJ,uBAAuBgE,MAAMwF,SAAS,CAAC/G,SAAD,CAAhB;EAFxB,GAGHuB,MAAMsG,mBACN,CAAA,IACA+B,sBAAsB5J,WAAWuB,MAAMsH,OAAlB,CALlB;AAQTuB,MAAI7C,OAAJ,OAAA,OAAA,CAAA,GACKF,aAAaE,MACb6C,IAAI7C,IAFT;AAKA6C,MAAI7C,OAAO;IACTC,UACE4C,IAAI7C,KAAKC,aAAa,SAASjG,MAAMuG,cAAcsC,IAAI7C,KAAKC;IAC9DT,SACEqD,IAAI7C,KAAKR,YAAY,SACjBxF,MAAMuG,cACJ,OACA,gBACFsC,IAAI7C,KAAKR;EARN;AAWX,SAAOqD;AACR;AAEM,SAASb,cACdD,cACAT,SACM;AAAA,MAFNS,iBAEM,QAAA;AAFNA,mBAA+B,CAAA;EAEzB;AAAA,MADNT,YACM,QAAA;AADNA,cAAoB,CAAA;EACd;AACN,MAAM5K,OAAOmB,OAAOnB,KAAKqL,YAAZ;AACbrL,OAAKE,QAAQ,SAACkM,MAAS;AACrB,QAAMC,iBAAiBtM,iBACrBqJ,cACAjI,OAAOnB,KAAKqI,WAAZ,CAFqC;AAKvC,QAAIiE,qBAAqB,CAAChO,eAAe+N,gBAAgBD,IAAjB;AAGxC,QAAIE,oBAAoB;AACtBA,2BACE1B,QAAQvK,OAAO,SAACoL,QAAD;AAAA,eAAYA,OAAOC,SAASU;MAA5B,CAAf,EAAiDG,WAAW;IAC/D;AAEDhF,aACE+E,oBACA,CAAA,MACOF,OADP,KAEE,wEACA,6DACA,QACA,gEACA,wDANF,EAOEzF,KAAK,GAPP,CAFM;EAWT,CAzBD;AA0BD;AC9LD,IAAM6F,YAAY,SAAZA,aAAY;AAAA,SAAmB;AAAnB;AAElB,SAASC,wBAAwB7J,SAAkB8J,MAAoB;AACrE9J,UAAQ4J,UAAS,CAAV,IAAgBE;AACxB;AAED,SAASC,mBAAmBhO,OAAuC;AACjE,MAAMkK,QAAQtH,IAAG;AAEjB,MAAI5C,UAAU,MAAM;AAClBkK,UAAM+D,YAAY9O;EACnB,OAAM;AACL+K,UAAM+D,YAAY7O;AAElB,QAAI0D,UAAU9C,KAAD,GAAS;AACpBkK,YAAMgE,YAAYlO,KAAlB;IACD,OAAM;AACL8N,8BAAwB5D,OAAOlK,KAAR;IACxB;EACF;AAED,SAAOkK;AACR;AAEM,SAASiE,WAAWhE,SAAyBxF,OAAoB;AACtE,MAAI7B,UAAU6B,MAAMwF,OAAP,GAAiB;AAC5B2D,4BAAwB3D,SAAS,EAAV;AACvBA,YAAQ+D,YAAYvJ,MAAMwF,OAA1B;EACD,WAAU,OAAOxF,MAAMwF,YAAY,YAAY;AAC9C,QAAIxF,MAAMqF,WAAW;AACnB8D,8BAAwB3D,SAASxF,MAAMwF,OAAhB;IACxB,OAAM;AACLA,cAAQiE,cAAczJ,MAAMwF;IAC7B;EACF;AACF;AAEM,SAASkE,YAAYC,QAAuC;AACjE,MAAMtI,MAAMsI,OAAOC;AACnB,MAAMC,cAAcnM,UAAU2D,IAAIyI,QAAL;AAE7B,SAAO;IACLzI;IACAmE,SAASqE,YAAYE,KAAK,SAACC,MAAD;AAAA,aAAUA,KAAKC,UAAUpI,SAASvH,aAAxB;IAAV,CAAjB;IACTiL,OAAOsE,YAAYE,KACjB,SAACC,MAAD;AAAA,aACEA,KAAKC,UAAUpI,SAASrH,WAAxB,KACAwP,KAAKC,UAAUpI,SAASpH,eAAxB;IAFF,CADK;IAKPyP,UAAUL,YAAYE,KAAK,SAACC,MAAD;AAAA,aACzBA,KAAKC,UAAUpI,SAAStH,cAAxB;IADyB,CAAjB;EARL;AAYR;AAEM,SAASiN,QACd5E,UAIA;AACA,MAAM+G,SAAS1L,IAAG;AAElB,MAAMoD,MAAMpD,IAAG;AACfoD,MAAIiI,YAAYjP;AAChBgH,MAAIlC,aAAa,cAAc,QAA/B;AACAkC,MAAIlC,aAAa,YAAY,IAA7B;AAEA,MAAMqG,UAAUvH,IAAG;AACnBuH,UAAQ8D,YAAYhP;AACpBkL,UAAQrG,aAAa,cAAc,QAAnC;AAEAqK,aAAWhE,SAAS5C,SAAS5C,KAAnB;AAEV2J,SAAOJ,YAAYlI,GAAnB;AACAA,MAAIkI,YAAY/D,OAAhB;AAEA2E,WAASvH,SAAS5C,OAAO4C,SAAS5C,KAA1B;AAER,WAASmK,SAASC,WAAkBC,WAAwB;AAC1D,QAAA,eAA8BX,YAAYC,MAAD,GAAlCtI,OAAP,aAAOA,KAAKmE,WAAZ,aAAYA,SAASD,QAArB,aAAqBA;AAErB,QAAI8E,UAAUzE,OAAO;AACnBvE,MAAAA,KAAIlC,aAAa,cAAckL,UAAUzE,KAAzC;IACD,OAAM;AACLvE,MAAAA,KAAIiJ,gBAAgB,YAApB;IACD;AAED,QAAI,OAAOD,UAAU/E,cAAc,UAAU;AAC3CjE,MAAAA,KAAIlC,aAAa,kBAAkBkL,UAAU/E,SAA7C;IACD,OAAM;AACLjE,MAAAA,KAAIiJ,gBAAgB,gBAApB;IACD;AAED,QAAID,UAAU5E,SAAS;AACrBpE,MAAAA,KAAIlC,aAAa,gBAAgB,EAAjC;IACD,OAAM;AACLkC,MAAAA,KAAIiJ,gBAAgB,cAApB;IACD;AAEDjJ,IAAAA,KAAItC,MAAM2G,WACR,OAAO2E,UAAU3E,aAAa,WACvB2E,UAAU3E,WADjB,OAEI2E,UAAU3E;AAEhB,QAAI2E,UAAU1E,MAAM;AAClBtE,MAAAA,KAAIlC,aAAa,QAAQkL,UAAU1E,IAAnC;IACD,OAAM;AACLtE,MAAAA,KAAIiJ,gBAAgB,MAApB;IACD;AAED,QACEF,UAAU5E,YAAY6E,UAAU7E,WAChC4E,UAAU/E,cAAcgF,UAAUhF,WAClC;AACAmE,iBAAWhE,UAAS5C,SAAS5C,KAAnB;IACX;AAED,QAAIqK,UAAU9E,OAAO;AACnB,UAAI,CAACA,OAAO;AACVlE,QAAAA,KAAIkI,YAAYF,mBAAmBgB,UAAU9E,KAAX,CAAlC;MACD,WAAU6E,UAAU7E,UAAU8E,UAAU9E,OAAO;AAC9ClE,QAAAA,KAAIkJ,YAAYhF,KAAhB;AACAlE,QAAAA,KAAIkI,YAAYF,mBAAmBgB,UAAU9E,KAAX,CAAlC;MACD;IACF,WAAUA,OAAO;AAChBlE,MAAAA,KAAIkJ,YAAYhF,KAAhB;IACD;EACF;AAED,SAAO;IACLoE;IACAQ;EAFK;AAIR;AAID3C,QAAOgD,UAAU;ACjHjB,IAAIC,YAAY;AAChB,IAAIC,qBAAsD,CAAA;AAGnD,IAAIC,mBAA+B,CAAA;AAE3B,SAASC,YACtBnM,WACAyJ,aACU;AACV,MAAMlI,QAAQ4I,cAAcnK,WAAD,OAAA,OAAA,CAAA,GACtBqH,cACAmC,uBAAuBrK,qBAAqBsK,WAAD,CAArB,CAFA,CAAA;AAQ3B,MAAI2C;AACJ,MAAIC;AACJ,MAAIC;AACJ,MAAIC,qBAAqB;AACzB,MAAIC,gCAAgC;AACpC,MAAIC,eAAe;AACnB,MAAIC,sBAAsB;AAC1B,MAAIC;AACJ,MAAIC;AACJ,MAAIC;AACJ,MAAIC,YAA8B,CAAA;AAClC,MAAIC,uBAAuBtP,SAASuP,aAAazL,MAAMwG,mBAApB;AACnC,MAAIkF;AAKJ,MAAMC,KAAKlB;AACX,MAAMmB,iBAAiB;AACvB,MAAMtE,UAAUhK,OAAO0C,MAAMsH,OAAP;AAEtB,MAAMpI,QAAQ;;IAEZ2M,WAAW;;IAEX/I,WAAW;;IAEXgJ,aAAa;;IAEbC,WAAW;;IAEXC,SAAS;EAVG;AAad,MAAMpJ,WAAqB;;IAEzB+I;IACAlN;IACAkL,QAAQ1L,IAAG;IACX2N;IACA5L;IACAd;IACAoI;;IAEA2E;IACAC;IACA1C,YAAAA;IACA2C;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EAnByB;AAyB3B,MAAI,CAACzM,MAAMwH,QAAQ;AACjB,QAAA,MAAa;AACXjD,gBAAU,MAAM,0CAAP;IACV;AAED,WAAO3B;EACR;AAKD,MAAA,gBAA2B5C,MAAMwH,OAAO5E,QAAb,GAApB+G,SAAP,cAAOA,QAAQQ,WAAf,cAAeA;AAEfR,SAAOxK,aAAa,mBAAkC,EAAtD;AACAwK,SAAOgC,KAAP,WAAoC/I,SAAS+I;AAE7C/I,WAAS+G,SAASA;AAClBlL,YAAUD,SAASoE;AACnB+G,SAAOnL,SAASoE;AAEhB,MAAM8J,eAAepF,QAAQqF,IAAI,SAACxE,QAAD;AAAA,WAAYA,OAAOhM,GAAGyG,QAAV;EAAZ,CAAZ;AACrB,MAAMgK,kBAAkBnO,UAAUoO,aAAa,eAAvB;AAExBC,eAAY;AACZC,8BAA2B;AAC3BC,eAAY;AAEZC,aAAW,YAAY,CAACrK,QAAD,CAAb;AAEV,MAAI5C,MAAMyH,cAAc;AACtByF,iBAAY;EACb;AAIDvD,SAAOrH,iBAAiB,cAAc,WAAM;AAC1C,QAAIM,SAAS5C,MAAMuG,eAAe3D,SAAS1D,MAAM4D,WAAW;AAC1DF,eAASqJ,mBAAT;IACD;EACF,CAJD;AAMAtC,SAAOrH,iBAAiB,cAAc,WAAM;AAC1C,QACEM,SAAS5C,MAAMuG,eACf3D,SAAS5C,MAAM2H,QAAQ5L,QAAQ,YAA/B,KAAgD,GAChD;AACAoR,kBAAW,EAAG7K,iBAAiB,aAAakJ,oBAA5C;IACD;EACF,CAPD;AASA,SAAO5I;AAKP,WAASwK,6BAAyD;AAChE,QAAO1F,QAAS9E,SAAS5C,MAAlB0H;AACP,WAAOlM,MAAMC,QAAQiM,KAAd,IAAuBA,QAAQ,CAACA,OAAO,CAAR;EACvC;AAED,WAAS2F,2BAAoC;AAC3C,WAAOD,2BAA0B,EAAG,CAAH,MAAU;EAC5C;AAED,WAASE,uBAAgC;AAAA,QAAA;AAEvC,WAAO,CAAC,GAAA,wBAAC1K,SAAS5C,MAAMwH,WAAhB,QAAC,sBAAuBgD;EACjC;AAED,WAAS+C,mBAA4B;AACnC,WAAO7B,iBAAiBjN;EACzB;AAED,WAAS0O,cAAwB;AAC/B,QAAMzL,SAAS6L,iBAAgB,EAAGC;AAClC,WAAO9L,SAAStC,iBAAiBsC,MAAD,IAAW5G;EAC5C;AAED,WAAS2S,6BAA6C;AACpD,WAAO/D,YAAYC,MAAD;EACnB;AAED,WAAS+D,SAASC,QAAyB;AAIzC,QACG/K,SAAS1D,MAAM6M,aAAa,CAACnJ,SAAS1D,MAAM4D,aAC7Cd,aAAaC,WACZmJ,oBAAoBA,iBAAiBxP,SAAS,SAC/C;AACA,aAAO;IACR;AAED,WAAOR,wBACLwH,SAAS5C,MAAMkG,OACfyH,SAAS,IAAI,GACb7H,aAAaI,KAHe;EAK/B;AAED,WAAS8G,aAAaY,UAAwB;AAAA,QAAxBA,aAAwB,QAAA;AAAxBA,iBAAW;IAAa;AAC5CjE,WAAO5K,MAAM8O,gBACXjL,SAAS5C,MAAMuG,eAAe,CAACqH,WAAW,KAAK;AACjDjE,WAAO5K,MAAM8G,SAAb,KAAyBjD,SAAS5C,MAAM6F;EACzC;AAED,WAASoH,WACPa,MACA7R,MACA8R,uBACM;AAAA,QADNA,0BACM,QAAA;AADNA,8BAAwB;IAClB;AACNrB,iBAAa9P,QAAQ,SAACoR,aAAgB;AACpC,UAAIA,YAAYF,IAAD,GAAQ;AACrBE,oBAAYF,IAAD,EAAX,MAAAE,aAAsB/R,IAAX;MACZ;IACF,CAJD;AAMA,QAAI8R,uBAAuB;AAAA,UAAA;AACzB,OAAA,kBAAAnL,SAAS5C,OAAM8N,IAAf,EAAA,MAAA,iBAAwB7R,IAAxB;IACD;EACF;AAED,WAASgS,6BAAmC;AAC1C,QAAOjI,OAAQpD,SAAS5C,MAAjBgG;AAEP,QAAI,CAACA,KAAKR,SAAS;AACjB;IACD;AAED,QAAM0I,OAAI,UAAWlI,KAAKR;AAC1B,QAAMmG,MAAKhC,OAAOgC;AAClB,QAAMwC,QAAQlR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,SAAjC;AAE9B0P,UAAMvR,QAAQ,SAACoN,MAAS;AACtB,UAAMoE,eAAepE,KAAKxB,aAAa0F,IAAlB;AAErB,UAAItL,SAAS1D,MAAM4D,WAAW;AAC5BkH,aAAK7K,aAAa+O,MAAME,eAAkBA,eAAN,MAAsBzC,MAAOA,GAAjE;MACD,OAAM;AACL,YAAM0C,YAAYD,gBAAgBA,aAAa3K,QAAQkI,KAAI,EAAzB,EAA6BjI,KAA7B;AAElC,YAAI2K,WAAW;AACbrE,eAAK7K,aAAa+O,MAAMG,SAAxB;QACD,OAAM;AACLrE,eAAKM,gBAAgB4D,IAArB;QACD;MACF;IACF,CAdD;EAeD;AAED,WAASnB,8BAAoC;AAC3C,QAAIH,mBAAmB,CAAChK,SAAS5C,MAAMgG,KAAKC,UAAU;AACpD;IACD;AAED,QAAMkI,QAAQlR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,SAAjC;AAE9B0P,UAAMvR,QAAQ,SAACoN,MAAS;AACtB,UAAIpH,SAAS5C,MAAMuG,aAAa;AAC9ByD,aAAK7K,aACH,iBACAyD,SAAS1D,MAAM4D,aAAakH,SAASuD,iBAAgB,IACjD,SACA,OAJN;MAMD,OAAM;AACLvD,aAAKM,gBAAgB,eAArB;MACD;IACF,CAXD;EAYD;AAED,WAASgE,mCAAyC;AAChDnB,gBAAW,EAAG1K,oBAAoB,aAAa+I,oBAA/C;AACAd,yBAAqBA,mBAAmB3N,OACtC,SAACwE,UAAD;AAAA,aAAcA,aAAaiK;IAA3B,CADmB;EAGtB;AAED,WAAS+C,gBAAgB7O,OAAsC;AAE7D,QAAIsC,aAAaC,SAAS;AACxB,UAAIiJ,gBAAgBxL,MAAM9D,SAAS,aAAa;AAC9C;MACD;IACF;AAED,QAAM4S,eACH9O,MAAM+O,gBAAgB/O,MAAM+O,aAAN,EAAqB,CAArB,KAA4B/O,MAAMkC;AAG3D,QACEgB,SAAS5C,MAAMuG,eACf9E,eAAekI,QAAQ6E,YAAT,GACd;AACA;IACD;AAGD,QACEvR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,SAAjC,EAA4CL,KAAK,SAACU,IAAD;AAAA,aAC/D2C,eAAe3C,IAAI0P,YAAL;IADiD,CAAjE,GAGA;AACA,UAAIxM,aAAaC,SAAS;AACxB;MACD;AAED,UACEW,SAAS1D,MAAM4D,aACfF,SAAS5C,MAAM2H,QAAQ5L,QAAQ,OAA/B,KAA2C,GAC3C;AACA;MACD;IACF,OAAM;AACLkR,iBAAW,kBAAkB,CAACrK,UAAUlD,KAAX,CAAnB;IACX;AAED,QAAIkD,SAAS5C,MAAMqG,gBAAgB,MAAM;AACvCzD,eAASqJ,mBAAT;AACArJ,eAASwJ,KAAT;AAKAnB,sCAAgC;AAChCzO,iBAAW,WAAM;AACfyO,wCAAgC;MACjC,CAFS;AAOV,UAAI,CAACrI,SAAS1D,MAAM6M,WAAW;AAC7B2C,4BAAmB;MACpB;IACF;EACF;AAED,WAASC,cAAoB;AAC3BzD,mBAAe;EAChB;AAED,WAAS0D,eAAqB;AAC5B1D,mBAAe;EAChB;AAED,WAAS2D,mBAAyB;AAChC,QAAMC,MAAM3B,YAAW;AACvB2B,QAAIxM,iBAAiB,aAAaiM,iBAAiB,IAAnD;AACAO,QAAIxM,iBAAiB,YAAYiM,iBAAiB7T,aAAlD;AACAoU,QAAIxM,iBAAiB,cAAcsM,cAAclU,aAAjD;AACAoU,QAAIxM,iBAAiB,aAAaqM,aAAajU,aAA/C;EACD;AAED,WAASgU,sBAA4B;AACnC,QAAMI,MAAM3B,YAAW;AACvB2B,QAAIrM,oBAAoB,aAAa8L,iBAAiB,IAAtD;AACAO,QAAIrM,oBAAoB,YAAY8L,iBAAiB7T,aAArD;AACAoU,QAAIrM,oBAAoB,cAAcmM,cAAclU,aAApD;AACAoU,QAAIrM,oBAAoB,aAAakM,aAAajU,aAAlD;EACD;AAED,WAASqU,kBAAkB5I,UAAkB6I,UAA4B;AACvEC,oBAAgB9I,UAAU,WAAM;AAC9B,UACE,CAACvD,SAAS1D,MAAM4D,aAChB6G,OAAO6D,cACP7D,OAAO6D,WAAW3L,SAAS8H,MAA3B,GACA;AACAqF,iBAAQ;MACT;IACF,CARc;EAShB;AAED,WAASE,iBAAiB/I,UAAkB6I,UAA4B;AACtEC,oBAAgB9I,UAAU6I,QAAX;EAChB;AAED,WAASC,gBAAgB9I,UAAkB6I,UAA4B;AACrE,QAAM3N,MAAMoM,2BAA0B,EAAGpM;AAEzC,aAASE,SAAS7B,OAA8B;AAC9C,UAAIA,MAAMkC,WAAWP,KAAK;AACxBD,oCAA4BC,KAAK,UAAUE,QAAhB;AAC3ByN,iBAAQ;MACT;IACF;AAID,QAAI7I,aAAa,GAAG;AAClB,aAAO6I,SAAQ;IAChB;AAED5N,gCAA4BC,KAAK,UAAUgK,4BAAhB;AAC3BjK,gCAA4BC,KAAK,OAAOE,QAAb;AAE3B8J,mCAA+B9J;EAChC;AAED,WAAS4N,GACPC,WACAC,SACAC,SACM;AAAA,QADNA,YACM,QAAA;AADNA,gBAA6C;IACvC;AACN,QAAMnB,QAAQlR,iBAAiB2F,SAAS5C,MAAM4H,iBAAiBnJ,SAAjC;AAC9B0P,UAAMvR,QAAQ,SAACoN,MAAS;AACtBA,WAAK1H,iBAAiB8M,WAAWC,SAASC,OAA1C;AACA/D,gBAAUlO,KAAK;QAAC2M;QAAMoF;QAAWC;QAASC;MAA3B,CAAf;IACD,CAHD;EAID;AAED,WAASxC,eAAqB;AAC5B,QAAIO,yBAAwB,GAAI;AAC9B8B,SAAG,cAAchI,YAAW;QAACxM,SAAS;MAAV,CAA1B;AACFwU,SAAG,YAAYI,cAA+B;QAAC5U,SAAS;MAAV,CAA5C;IACH;AAEDkC,kBAAc+F,SAAS5C,MAAM2H,OAAhB,EAAyB/K,QAAQ,SAACwS,WAAc;AAC3D,UAAIA,cAAc,UAAU;AAC1B;MACD;AAEDD,SAAGC,WAAWjI,UAAZ;AAEF,cAAQiI,WAAR;QACE,KAAK;AACHD,aAAG,cAAcI,YAAf;AACF;QACF,KAAK;AACHJ,aAAGlM,SAAS,aAAa,QAAQuM,gBAA/B;AACF;QACF,KAAK;AACHL,aAAG,YAAYK,gBAAb;AACF;MATJ;IAWD,CAlBD;EAmBD;AAED,WAASC,kBAAwB;AAC/BlE,cAAU3O,QAAQ,SAAA,MAAyD;AAAA,UAAvDoN,OAAuD,KAAvDA,MAAMoF,YAAiD,KAAjDA,WAAWC,UAAsC,KAAtCA,SAASC,UAA6B,KAA7BA;AAC5CtF,WAAKvH,oBAAoB2M,WAAWC,SAASC,OAA7C;IACD,CAFD;AAGA/D,gBAAY,CAAA;EACb;AAED,WAASpE,WAAUzH,OAAoB;AAAA,QAAA;AACrC,QAAIgQ,0BAA0B;AAE9B,QACE,CAAC9M,SAAS1D,MAAM2M,aAChB8D,uBAAuBjQ,KAAD,KACtBuL,+BACA;AACA;IACD;AAED,QAAM2E,eAAa,oBAAAxE,qBAAgB,OAAhB,SAAA,kBAAkBxP,UAAS;AAE9CwP,uBAAmB1L;AACnBgM,oBAAgBhM,MAAMgM;AAEtBqB,gCAA2B;AAE3B,QAAI,CAACnK,SAAS1D,MAAM4D,aAAaxE,aAAaoB,KAAD,GAAS;AAKpDgL,yBAAmB9N,QAAQ,SAAC2E,UAAD;AAAA,eAAcA,SAAS7B,KAAD;MAAtB,CAA3B;IACD;AAGD,QACEA,MAAM9D,SAAS,YACdgH,SAAS5C,MAAM2H,QAAQ5L,QAAQ,YAA/B,IAA+C,KAC9CiP,uBACFpI,SAAS5C,MAAMqG,gBAAgB,SAC/BzD,SAAS1D,MAAM4D,WACf;AACA4M,gCAA0B;IAC3B,OAAM;AACLxC,mBAAaxN,KAAD;IACb;AAED,QAAIA,MAAM9D,SAAS,SAAS;AAC1BoP,2BAAqB,CAAC0E;IACvB;AAED,QAAIA,2BAA2B,CAACE,YAAY;AAC1CC,mBAAanQ,KAAD;IACb;EACF;AAED,WAAS+L,YAAY/L,OAAyB;AAC5C,QAAMkC,SAASlC,MAAMkC;AACrB,QAAMkO,gCACJvC,iBAAgB,EAAG1L,SAASD,MAA5B,KAAuC+H,OAAO9H,SAASD,MAAhB;AAEzC,QAAIlC,MAAM9D,SAAS,eAAekU,+BAA+B;AAC/D;IACD;AAED,QAAMrQ,iBAAiBsQ,oBAAmB,EACvC7S,OAAOyM,MADa,EAEpBgD,IAAI,SAAChD,SAAW;AAAA,UAAA;AACf,UAAM/G,YAAW+G,QAAOnL;AACxB,UAAMU,UAAK,wBAAG0D,UAASgJ,mBAAZ,OAAA,SAAG,sBAAyB1M;AAEvC,UAAIA,QAAO;AACT,eAAO;UACLY,YAAY6J,QAAOqG,sBAAP;UACZjQ,aAAab;UACbc;QAHK;MAKR;AAED,aAAO;IACR,CAfoB,EAgBpBjD,OAAOC,OAhBa;AAkBvB,QAAIwC,iCAAiCC,gBAAgBC,KAAjB,GAAyB;AAC3D4O,uCAAgC;AAChCuB,mBAAanQ,KAAD;IACb;EACF;AAED,WAAS6P,aAAa7P,OAAyB;AAC7C,QAAMuQ,aACJN,uBAAuBjQ,KAAD,KACrBkD,SAAS5C,MAAM2H,QAAQ5L,QAAQ,OAA/B,KAA2C,KAAKiP;AAEnD,QAAIiF,YAAY;AACd;IACD;AAED,QAAIrN,SAAS5C,MAAMuG,aAAa;AAC9B3D,eAASyJ,sBAAsB3M,KAA/B;AACA;IACD;AAEDmQ,iBAAanQ,KAAD;EACb;AAED,WAAS8P,iBAAiB9P,OAAyB;AACjD,QACEkD,SAAS5C,MAAM2H,QAAQ5L,QAAQ,SAA/B,IAA4C,KAC5C2D,MAAMkC,WAAW2L,iBAAgB,GACjC;AACA;IACD;AAGD,QACE3K,SAAS5C,MAAMuG,eACf7G,MAAMwQ,iBACNvG,OAAO9H,SAASnC,MAAMwQ,aAAtB,GACA;AACA;IACD;AAEDL,iBAAanQ,KAAD;EACb;AAED,WAASiQ,uBAAuBjQ,OAAuB;AACrD,WAAOsC,aAAaC,UAChBoL,yBAAwB,MAAO3N,MAAM9D,KAAKG,QAAQ,OAAnB,KAA+B,IAC9D;EACL;AAED,WAASoU,uBAA6B;AACpCC,0BAAqB;AAErB,QAAA,mBAMIxN,SAAS5C,OALXuH,gBADF,iBACEA,eACA9J,YAFF,iBAEEA,WACA4C,SAHF,iBAGEA,QACA+F,yBAJF,iBAIEA,wBACAK,iBALF,iBAKEA;AAGF,QAAMlB,QAAQ+H,qBAAoB,IAAK5D,YAAYC,MAAD,EAASpE,QAAQ;AAEnE,QAAM8K,oBAAoBjK,yBACtB;MACE4J,uBAAuB5J;MACvBkK,gBACElK,uBAAuBkK,kBAAkB/C,iBAAgB;IAH7D,IAKA9O;AAEJ,QAAM8R,gBAA8D;MAClEnI,MAAM;MACNoI,SAAS;MACTC,OAAO;MACPC,UAAU,CAAC,eAAD;MACVvU,IALkE,SAAA,GAAA,OAKtD;AAAA,YAAR+C,SAAQ,MAARA;AACF,YAAIoO,qBAAoB,GAAI;AAC1B,cAAA,wBAAcG,2BAA0B,GAAjCpM,MAAP,sBAAOA;AAEP,WAAC,aAAa,oBAAoB,SAAlC,EAA6CzE,QAAQ,SAACsR,MAAS;AAC7D,gBAAIA,SAAS,aAAa;AACxB7M,kBAAIlC,aAAa,kBAAkBD,OAAMzB,SAAzC;YACD,OAAM;AACL,kBAAIyB,OAAMyR,WAAWhH,OAAjB,iBAAuCuE,IAAvC,GAAgD;AAClD7M,oBAAIlC,aAAJ,UAAyB+O,MAAQ,EAAjC;cACD,OAAM;AACL7M,oBAAIiJ,gBAAJ,UAA4B4D,IAA5B;cACD;YACF;UACF,CAVD;AAYAhP,UAAAA,OAAMyR,WAAWhH,SAAS,CAAA;QAC3B;MACF;IAvBiE;AA6BpE,QAAMiH,YAAsC,CAC1C;MACExI,MAAM;MACNkH,SAAS;QACPjP;MADO;IAFX,GAMA;MACE+H,MAAM;MACNkH,SAAS;QACPuB,SAAS;UACPtQ,KAAK;UACLG,QAAQ;UACRE,MAAM;UACNG,OAAO;QAJA;MADF;IAFX,GAWA;MACEqH,MAAM;MACNkH,SAAS;QACPuB,SAAS;MADF;IAFX,GAMA;MACEzI,MAAM;MACNkH,SAAS;QACPwB,UAAU,CAACrK;MADJ;IAFX,GAMA8J,aA9B0C;AAiC5C,QAAIjD,qBAAoB,KAAM/H,OAAO;AACnCqL,gBAAUvT,KAAK;QACb+K,MAAM;QACNkH,SAAS;UACPhQ,SAASiG;UACTsL,SAAS;QAFF;MAFI,CAAf;IAOD;AAEDD,cAAUvT,KAAV,MAAAuT,YAAmBrJ,iBAAa,OAAb,SAAAA,cAAeqJ,cAAa,CAAA,CAAtC;AAEThO,aAASgJ,iBAAiBmF,GACxBV,mBACA1G,QAFoC,OAAA,OAAA,CAAA,GAI/BpC,eAJ+B;MAKlC9J;MACA6N;MACAsF;IAPkC,CAAA,CAAA;EAUvC;AAED,WAASR,wBAA8B;AACrC,QAAIxN,SAASgJ,gBAAgB;AAC3BhJ,eAASgJ,eAAea,QAAxB;AACA7J,eAASgJ,iBAAiB;IAC3B;EACF;AAED,WAASoF,QAAc;AACrB,QAAOjL,WAAYnD,SAAS5C,MAArB+F;AAEP,QAAIyH;AAOJ,QAAMxD,OAAOuD,iBAAgB;AAE7B,QACG3K,SAAS5C,MAAMuG,eAAeR,aAAalL,2BAC5CkL,aAAa,UACb;AACAyH,mBAAaxD,KAAKwD;IACnB,OAAM;AACLA,mBAAaxR,uBAAuB+J,UAAU,CAACiE,IAAD,CAAX;IACpC;AAID,QAAI,CAACwD,WAAW3L,SAAS8H,MAApB,GAA6B;AAChC6D,iBAAWjE,YAAYI,MAAvB;IACD;AAED/G,aAAS1D,MAAM6M,YAAY;AAE3BoE,yBAAoB;AAGpB,QAAA,MAAa;AAEXlM,eACErB,SAAS5C,MAAMuG,eACbR,aAAaD,aAAaC,YAC1BiE,KAAKiH,uBAAuBtH,QAC9B,CACE,gEACA,qEACA,4BACA,QACA,oEACA,qDACA,QACA,sEACA,+DACA,wBACA,QACA,wEAZF,EAaEtG,KAAK,GAbP,CAJM;IAmBT;EACF;AAED,WAAS0M,sBAAuC;AAC9C,WAAOrS,UACLiM,OAAOhL,iBAAiB,mBAAxB,CADc;EAGjB;AAED,WAASuO,aAAaxN,OAAqB;AACzCkD,aAASqJ,mBAAT;AAEA,QAAIvM,OAAO;AACTuN,iBAAW,aAAa,CAACrK,UAAUlD,KAAX,CAAd;IACX;AAEDmP,qBAAgB;AAEhB,QAAI3I,QAAQwH,SAAS,IAAD;AACpB,QAAA,wBAAiCN,2BAA0B,GAApD8D,aAAP,sBAAA,CAAA,GAAmBC,aAAnB,sBAAA,CAAA;AAEA,QAAInP,aAAaC,WAAWiP,eAAe,UAAUC,YAAY;AAC/DjL,cAAQiL;IACT;AAED,QAAIjL,OAAO;AACT2E,oBAAcrO,WAAW,WAAM;AAC7BoG,iBAASuJ,KAAT;MACD,GAAEjG,KAFqB;IAGzB,OAAM;AACLtD,eAASuJ,KAAT;IACD;EACF;AAED,WAAS0D,aAAanQ,OAAoB;AACxCkD,aAASqJ,mBAAT;AAEAgB,eAAW,eAAe,CAACrK,UAAUlD,KAAX,CAAhB;AAEV,QAAI,CAACkD,SAAS1D,MAAM4D,WAAW;AAC7B4L,0BAAmB;AAEnB;IACD;AAMD,QACE9L,SAAS5C,MAAM2H,QAAQ5L,QAAQ,YAA/B,KAAgD,KAChD6G,SAAS5C,MAAM2H,QAAQ5L,QAAQ,OAA/B,KAA2C,KAC3C,CAAC,cAAc,WAAf,EAA4BA,QAAQ2D,MAAM9D,IAA1C,KAAmD,KACnDoP,oBACA;AACA;IACD;AAED,QAAM9E,QAAQwH,SAAS,KAAD;AAEtB,QAAIxH,OAAO;AACT4E,oBAActO,WAAW,WAAM;AAC7B,YAAIoG,SAAS1D,MAAM4D,WAAW;AAC5BF,mBAASwJ,KAAT;QACD;MACF,GAAElG,KAJqB;IAKzB,OAAM;AAGL6E,mCAA6BqG,sBAAsB,WAAM;AACvDxO,iBAASwJ,KAAT;MACD,CAFiD;IAGnD;EACF;AAKD,WAASE,SAAe;AACtB1J,aAAS1D,MAAM2M,YAAY;EAC5B;AAED,WAASU,UAAgB;AAGvB3J,aAASwJ,KAAT;AACAxJ,aAAS1D,MAAM2M,YAAY;EAC5B;AAED,WAASI,qBAA2B;AAClC1P,iBAAasO,WAAD;AACZtO,iBAAauO,WAAD;AACZuG,yBAAqBtG,0BAAD;EACrB;AAED,WAASmB,SAASnE,cAAoC;AAEpD,QAAA,MAAa;AACX9D,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,UAAD,CAApD;IACT;AAED,QAAIP,SAAS1D,MAAM4M,aAAa;AAC9B;IACD;AAEDmB,eAAW,kBAAkB,CAACrK,UAAUmF,YAAX,CAAnB;AAEV0H,oBAAe;AAEf,QAAMrF,YAAYxH,SAAS5C;AAC3B,QAAMqK,YAAYzB,cAAcnK,WAAD,OAAA,OAAA,CAAA,GAC1B2L,WACAxM,qBAAqBmK,YAAD,GAFM;MAG7BzB,kBAAkB;IAHW,CAAA,CAAA;AAM/B1D,aAAS5C,QAAQqK;AAEjByC,iBAAY;AAEZ,QAAI1C,UAAU5D,wBAAwB6D,UAAU7D,qBAAqB;AACnE8H,uCAAgC;AAChC9C,6BAAuBtP,SACrBuP,aACApB,UAAU7D,mBAFmB;IAIhC;AAGD,QAAI4D,UAAUxC,iBAAiB,CAACyC,UAAUzC,eAAe;AACvD3K,uBAAiBmN,UAAUxC,aAAX,EAA0BhL,QAAQ,SAACoN,MAAS;AAC1DA,aAAKM,gBAAgB,eAArB;MACD,CAFD;IAGD,WAAUD,UAAUzC,eAAe;AAClCnJ,gBAAU6L,gBAAgB,eAA1B;IACD;AAEDyC,gCAA2B;AAC3BC,iBAAY;AAEZ,QAAI7C,UAAU;AACZA,eAASC,WAAWC,SAAZ;IACT;AAED,QAAIzH,SAASgJ,gBAAgB;AAC3BuE,2BAAoB;AAMpBJ,0BAAmB,EAAGnT,QAAQ,SAAC0U,cAAiB;AAG9CF,8BAAsBE,aAAa9S,OAAQoN,eAAgB2F,WAAtC;MACtB,CAJD;IAKD;AAEDtE,eAAW,iBAAiB,CAACrK,UAAUmF,YAAX,CAAlB;EACX;AAED,WAASyB,YAAWhE,SAAwB;AAC1C5C,aAASsJ,SAAS;MAAC1G;IAAD,CAAlB;EACD;AAED,WAAS2G,OAAa;AAEpB,QAAA,MAAa;AACXlI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,MAAD,CAApD;IACT;AAGD,QAAMqO,mBAAmB5O,SAAS1D,MAAM4D;AACxC,QAAMgJ,cAAclJ,SAAS1D,MAAM4M;AACnC,QAAM2F,aAAa,CAAC7O,SAAS1D,MAAM2M;AACnC,QAAM6F,0BACJ1P,aAAaC,WAAW,CAACW,SAAS5C,MAAM0H;AAC1C,QAAMvB,WAAW/K,wBACfwH,SAAS5C,MAAMmG,UACf,GACAL,aAAaK,QAHyB;AAMxC,QACEqL,oBACA1F,eACA2F,cACAC,yBACA;AACA;IACD;AAKD,QAAInE,iBAAgB,EAAGV,aAAa,UAAhC,GAA6C;AAC/C;IACD;AAEDI,eAAW,UAAU,CAACrK,QAAD,GAAY,KAAvB;AACV,QAAIA,SAAS5C,MAAMiH,OAAOrE,QAAtB,MAAoC,OAAO;AAC7C;IACD;AAEDA,aAAS1D,MAAM4D,YAAY;AAE3B,QAAIwK,qBAAoB,GAAI;AAC1B3D,aAAO5K,MAAM4S,aAAa;IAC3B;AAED3E,iBAAY;AACZ6B,qBAAgB;AAEhB,QAAI,CAACjM,SAAS1D,MAAM6M,WAAW;AAC7BpC,aAAO5K,MAAM6S,aAAa;IAC3B;AAID,QAAItE,qBAAoB,GAAI;AAC1B,UAAA,yBAAuBG,2BAA0B,GAA1CpM,MAAP,uBAAOA,KAAKmE,UAAZ,uBAAYA;AACZ5G,4BAAsB,CAACyC,KAAKmE,OAAN,GAAgB,CAAjB;IACtB;AAED8F,oBAAgB,SAAAA,iBAAY;AAAA,UAAA;AAC1B,UAAI,CAAC1I,SAAS1D,MAAM4D,aAAaqI,qBAAqB;AACpD;MACD;AAEDA,4BAAsB;AAGtB,WAAKxB,OAAOkI;AAEZlI,aAAO5K,MAAM6S,aAAahP,SAAS5C,MAAMyG;AAEzC,UAAI6G,qBAAoB,KAAM1K,SAAS5C,MAAMsF,WAAW;AACtD,YAAA,yBAAuBmI,2BAA0B,GAA1CpM,OAAP,uBAAOA,KAAKmE,WAAZ,uBAAYA;AACZ5G,8BAAsB,CAACyC,MAAKmE,QAAN,GAAgBW,QAAjB;AACrBlH,2BAAmB,CAACoC,MAAKmE,QAAN,GAAgB,SAAjB;MACnB;AAEDyI,iCAA0B;AAC1BlB,kCAA2B;AAE3B5P,mBAAawN,kBAAkB/H,QAAnB;AAIZ,OAAA,yBAAAA,SAASgJ,mBAAT,OAAA,SAAA,uBAAyB2F,YAAzB;AAEAtE,iBAAW,WAAW,CAACrK,QAAD,CAAZ;AAEV,UAAIA,SAAS5C,MAAMsF,aAAagI,qBAAoB,GAAI;AACtD4B,yBAAiB/I,UAAU,WAAM;AAC/BvD,mBAAS1D,MAAM8M,UAAU;AACzBiB,qBAAW,WAAW,CAACrK,QAAD,CAAZ;QACX,CAHe;MAIjB;IACF;AAEDoO,UAAK;EACN;AAED,WAAS5E,OAAa;AAEpB,QAAA,MAAa;AACXnI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,MAAD,CAApD;IACT;AAGD,QAAM2O,kBAAkB,CAAClP,SAAS1D,MAAM4D;AACxC,QAAMgJ,cAAclJ,SAAS1D,MAAM4M;AACnC,QAAM2F,aAAa,CAAC7O,SAAS1D,MAAM2M;AACnC,QAAM1F,WAAW/K,wBACfwH,SAAS5C,MAAMmG,UACf,GACAL,aAAaK,QAHyB;AAMxC,QAAI2L,mBAAmBhG,eAAe2F,YAAY;AAChD;IACD;AAEDxE,eAAW,UAAU,CAACrK,QAAD,GAAY,KAAvB;AACV,QAAIA,SAAS5C,MAAM+G,OAAOnE,QAAtB,MAAoC,OAAO;AAC7C;IACD;AAEDA,aAAS1D,MAAM4D,YAAY;AAC3BF,aAAS1D,MAAM8M,UAAU;AACzBb,0BAAsB;AACtBH,yBAAqB;AAErB,QAAIsC,qBAAoB,GAAI;AAC1B3D,aAAO5K,MAAM4S,aAAa;IAC3B;AAEDrD,qCAAgC;AAChCI,wBAAmB;AACnB1B,iBAAa,IAAD;AAEZ,QAAIM,qBAAoB,GAAI;AAC1B,UAAA,yBAAuBG,2BAA0B,GAA1CpM,MAAP,uBAAOA,KAAKmE,UAAZ,uBAAYA;AAEZ,UAAI5C,SAAS5C,MAAMsF,WAAW;AAC5B1G,8BAAsB,CAACyC,KAAKmE,OAAN,GAAgBW,QAAjB;AACrBlH,2BAAmB,CAACoC,KAAKmE,OAAN,GAAgB,QAAjB;MACnB;IACF;AAEDyI,+BAA0B;AAC1BlB,gCAA2B;AAE3B,QAAInK,SAAS5C,MAAMsF,WAAW;AAC5B,UAAIgI,qBAAoB,GAAI;AAC1ByB,0BAAkB5I,UAAUvD,SAAS4J,OAApB;MAClB;IACF,OAAM;AACL5J,eAAS4J,QAAT;IACD;EACF;AAED,WAASH,sBAAsB3M,OAAyB;AAEtD,QAAA,MAAa;AACXuE,eACErB,SAAS1D,MAAM4M,aACf3I,wBAAwB,uBAAD,CAFjB;IAIT;AAEDgK,gBAAW,EAAG7K,iBAAiB,aAAakJ,oBAA5C;AACArO,iBAAauN,oBAAoBc,oBAArB;AACZA,yBAAqB9L,KAAD;EACrB;AAED,WAAS8M,UAAgB;AAEvB,QAAA,MAAa;AACXvI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,SAAD,CAApD;IACT;AAED,QAAIP,SAAS1D,MAAM4D,WAAW;AAC5BF,eAASwJ,KAAT;IACD;AAED,QAAI,CAACxJ,SAAS1D,MAAM6M,WAAW;AAC7B;IACD;AAEDqE,0BAAqB;AAKrBL,wBAAmB,EAAGnT,QAAQ,SAAC0U,cAAiB;AAC9CA,mBAAa9S,OAAQgO,QAArB;IACD,CAFD;AAIA,QAAI7C,OAAO6D,YAAY;AACrB7D,aAAO6D,WAAWjD,YAAYZ,MAA9B;IACD;AAEDgB,uBAAmBA,iBAAiB5N,OAAO,SAACgV,GAAD;AAAA,aAAOA,MAAMnP;IAAb,CAAxB;AAEnBA,aAAS1D,MAAM6M,YAAY;AAC3BkB,eAAW,YAAY,CAACrK,QAAD,CAAb;EACX;AAED,WAAS6J,UAAgB;AAEvB,QAAA,MAAa;AACXxI,eAASrB,SAAS1D,MAAM4M,aAAa3I,wBAAwB,SAAD,CAApD;IACT;AAED,QAAIP,SAAS1D,MAAM4M,aAAa;AAC9B;IACD;AAEDlJ,aAASqJ,mBAAT;AACArJ,aAAS4J,QAAT;AAEAiD,oBAAe;AAEf,WAAOhR,UAAUD;AAEjBoE,aAAS1D,MAAM4M,cAAc;AAE7BmB,eAAW,aAAa,CAACrK,QAAD,CAAd;EACX;AACF;AC/mCD,SAASoP,MACPtN,SACAuN,eACuB;AAAA,MADvBA,kBACuB,QAAA;AADvBA,oBAAgC,CAAA;EACT;AACvB,MAAM3K,UAAUxB,aAAawB,QAAQpK,OAAO+U,cAAc3K,WAAW,CAAA,CAArD;AAGhB,MAAA,MAAa;AACX7C,oBAAgBC,OAAD;AACfsD,kBAAciK,eAAe3K,OAAhB;EACd;AAEDvE,2BAAwB;AAExB,MAAMmF,cAA2B,OAAA,OAAA,CAAA,GAAO+J,eAAP;IAAsB3K;EAAtB,CAAA;AAEjC,MAAM4K,WAAWxT,mBAAmBgG,OAAD;AAGnC,MAAA,MAAa;AACX,QAAMyN,yBAAyBhU,UAAU+J,YAAY1C,OAAb;AACxC,QAAM4M,gCAAgCF,SAASjJ,SAAS;AACxDhF,aACEkO,0BAA0BC,+BAC1B,CACE,sEACA,qEACA,qEACA,QACA,uEACA,oDACA,QACA,mCACA,2CATF,EAUE/O,KAAK,GAVP,CAFM;EAcT;AAED,MAAMgP,YAAYH,SAASpU,OACzB,SAACC,KAAKU,WAA0B;AAC9B,QAAMmE,WAAWnE,aAAamM,YAAYnM,WAAWyJ,WAAZ;AAEzC,QAAItF,UAAU;AACZ7E,UAAIV,KAAKuF,QAAT;IACD;AAED,WAAO7E;EACR,GACD,CAAA,CAVgB;AAalB,SAAOI,UAAUuG,OAAD,IAAY2N,UAAU,CAAD,IAAMA;AAC5C;AAEDL,MAAMlM,eAAeA;AACrBkM,MAAMlK,kBAAkBA;AACxBkK,MAAMhQ,eAAeA;AC9CrB,IAAMsQ,sBAAqE,OAAA,OAAA,CAAA,GACtEC,IADsE;EAEzEC,QAFyE,SAAA,OAAA,MAEzD;AAAA,QAARC,QAAQ,KAARA;AACN,QAAMC,gBAAgB;MACpBC,QAAQ;QACNC,UAAUH,MAAMI,QAAQC;QACxBC,MAAM;QACNC,KAAK;QACLC,QAAQ;MAJF;MAMRC,OAAO;QACLN,UAAU;MADL;MAGPO,WAAW,CAAA;IAVS;AAatBC,WAAOC,OAAOZ,MAAMa,SAASX,OAAOY,OAAOb,cAAcC,MAAzD;AACAF,UAAMe,SAASd;AAEf,QAAID,MAAMa,SAASJ,OAAO;AACxBE,aAAOC,OAAOZ,MAAMa,SAASJ,MAAMK,OAAOb,cAAcQ,KAAxD;IACD;EAIF;AAzBwE,CAAA;AMhB3EO,MAAMC,gBAAgB;EAACC,QAAAA;AAAD,CAAtB;;;;IC2Da,uBAAc;EA6CzB,YAAY,EACV,QACA,SACA,MACA,eAAe,CAAA,GACf,cAAc,KACd,WAAU,GACU;AA7Cf,SAAW,cAAG;AAUd,SAAA,aAAiE,CAAC,EACvE,MAAAC,OACA,OACA,MACA,GAAE,MACC;AACH,YAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,YAAM,EAAE,MAAK,IAAK;AAKlB,YAAM,mBAAmB,CAAC,IAAI,YAAY,MAAM,EAAE,EAAE,UAAU,gBAAgB,MAAM,SAAS;AAK7F,YAAM,gBAAgB,KAAK,QAAQ,SAAS,SAAS,aAAa;AAElE,YAAM,iBAAiBA,MAAK,SAAQ,KAAM;AAE1C,UAAI,CAAC,kBAAkB,SAAS,oBAAoB,CAAC,KAAK,OAAO,YAAY;AAC3E,eAAO;;AAGT,aAAO;IACT;AA6BA,SAAgB,mBAAG,MAAK;AACtB,WAAK,cAAc;IACrB;AAEA,SAAgB,mBAAG,MAAK;AACtB,WAAK,KAAI;IACX;AAEA,SAAY,eAAG,MAAK;AAElB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;IAChD;AAEA,SAAA,cAAc,CAAC,EAAE,MAAK,MAA6B;;AACjD,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;;AAGF,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,oBAAiB,KAAA,KAAK,QAAQ,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,MAAM,aAAqB,IAAG;AAC1F;;AAGF,WACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,mBAAkB,KAAK,OAAO,KAAK,KAC1C;AACA;;AAGF,WAAK,KAAI;IACX;AAEA,SAAA,mBAAmB,CAAC,UAAqB;AACvC,WAAK,YAAY,EAAE,MAAK,CAAE;IAC5B;AA0CA,SAAA,wBAAwB,CAACA,OAAkB,aAA0B;AACnE,YAAM,mBAAmB,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,UAAU,GAAGA,MAAK,MAAM,SAAS;AACrE,YAAM,aAAa,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,IAAI,GAAGA,MAAK,MAAM,GAAG;AAEnD,UAAI,CAAC,oBAAoB,CAAC,YAAY;AACpC;;AAGF,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;;AAGvC,WAAK,sBAAsB,OAAO,WAAW,MAAK;AAChD,aAAK,cAAcA,OAAM,kBAAkB,YAAY,QAAQ;MACjE,GAAG,KAAK,WAAW;IACrB;AAEA,SAAa,gBAAG,CAACA,OAAkB,kBAA2B,YAAqB,aAA0B;;AAC3G,YAAM,EAAE,OAAO,UAAS,IAAKA;AAC7B,YAAM,EAAE,UAAS,IAAK;AAEtB,YAAM,SAAS,CAAC,oBAAoB,CAAC;AAErC,UAAI,aAAa,QAAQ;AACvB;;AAGF,WAAK,cAAa;AAGlB,YAAM,EAAE,OAAM,IAAK;AACnB,YAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC;AAC7D,YAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,IAAI,GAAG,CAAC;AAEzD,YAAMC,eAAa,KAAA,KAAK,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAA;QACnC,QAAQ,KAAK;QACb,SAAS,KAAK;QACd,MAAAD;QACA;QACA;QACA;QACA;MACD,CAAA;AAED,UAAI,CAACC,aAAY;AACf,aAAK,KAAI;AAET;;AAGF,OAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;QACnB,0BACE,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,4BACf,MAAK;AACP,cAAI,gBAAgB,MAAM,SAAS,GAAG;AACpC,gBAAI,OAAOD,MAAK,QAAQ,IAAI;AAE5B,gBAAI,MAAM;AACR,oBAAM,kBAAkB,KAAK,QAAQ,kBAAkB,OAAO,KAAK,cAAc,0BAA0B;AAE3G,kBAAI,iBAAiB;AACnB,uBAAO,gBAAgB;;AAGzB,kBAAI,MAAM;AACR,uBAAO,KAAK,sBAAqB;;;;AAKvC,iBAAO,aAAaA,OAAM,MAAM,EAAE;QACpC;MACH,CAAA;AAED,WAAK,KAAI;IACX;AA3KE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,cAAc;AAEnB,QAAI,YAAY;AACd,WAAK,aAAa;;AAGpB,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACnF,SAAK,KAAK,IAAI,iBAAiB,aAAa,KAAK,gBAAgB;AACjE,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,SAAK,eAAe;AAEpB,SAAK,QAAQ,OAAM;AACnB,SAAK,QAAQ,MAAM,aAAa;;EAwClC,gBAAa;AACX,UAAM,EAAE,SAAS,cAAa,IAAK,KAAK,OAAO;AAC/C,UAAM,mBAAmB,CAAC,CAAC,cAAc;AAEzC,QAAI,KAAK,SAAS,CAAC,kBAAkB;AACnC;;AAGF,SAAK,QAAQ,kBAAM,eAAe;MAChC,UAAU;MACV,wBAAwB;MACxB,SAAS,KAAK;MACd,aAAa;MACb,SAAS;MACT,WAAW;MACX,aAAa;MACb,GAAG,KAAK;IACT,CAAA;AAGD,QAAI,KAAK,MAAM,OAAO,YAAY;AAC/B,WAAK,MAAM,OAAO,WAA2B,iBAAiB,QAAQ,KAAK,gBAAgB;;;EAIhG,OAAO,MAAkB,UAAsB;AAC7C,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,oBAAoB,MAAM,UAAU,SAAS,MAAM,UAAU;AAEnE,QAAI,KAAK,cAAc,KAAK,mBAAmB;AAC7C,WAAK,sBAAsB,MAAM,QAAQ;AACzC;;AAGF,UAAM,mBAAmB,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,UAAU,GAAG,KAAK,MAAM,SAAS;AACrE,UAAM,aAAa,EAAC,aAAA,QAAA,aAAQ,SAAA,SAAR,SAAU,IAAI,GAAG,KAAK,MAAM,GAAG;AAEnD,SAAK,cAAc,MAAM,kBAAkB,YAAY,QAAQ;;EAgFjE,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,UAAO;;AACL,SAAI,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,YAAY;AAChC,WAAK,MAAM,OAAO,WAA2B,oBAC5C,QACA,KAAK,gBAAgB;;AAGzB,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;AACnB,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACtF,SAAK,KAAK,IAAI,oBAAoB,aAAa,KAAK,gBAAgB;AACpE,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;;AAE3C;AAEY,IAAA,mBAAmB,CAAC,YAAkC;AACjE,SAAO,IAAI,OAAO;IAChB,KACE,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;IACrF,MAAM,UAAQ,IAAI,eAAe,EAAE,MAAM,GAAG,QAAO,CAAE;EACtD,CAAA;AACH;AC5Sa,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,SAAS;MACT,cAAc,CAAA;MACd,WAAW;MACX,aAAa;MACb,YAAY;;;EAIhB,wBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAA;;AAGT,WAAO;MACL,iBAAiB;QACf,WAAW,KAAK,QAAQ;QACxB,QAAQ,KAAK;QACb,SAAS,KAAK,QAAQ;QACtB,cAAc,KAAK,QAAQ;QAC3B,aAAa,KAAK,QAAQ;QAC1B,YAAY,KAAK,QAAQ;OAC1B;;;AAGN,CAAA;;;ICUY,yBAAgB;EAanB,eAAe,MAAoB;AACzC,WAAO,QAAQ,MAAM,EAAE,iBAAiB,6BAA6B,KAAK,OAAO,MAAM,EAAC,CAAE;;EAuB5F,YAAY,EACV,QAAQ,SAAS,MAAM,eAAe,CAAA,GAAI,WAAU,GAC9B;AAhCjB,SAAW,cAAG;AAUd,SAAU,aAAyD,CAAC,EAAE,MAAAE,OAAM,MAAK,MAAM;AAC5F,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,SAAS,MAAK,IAAK;AAC3B,YAAM,cAAc,QAAQ,UAAU;AAEtC,YAAM,mBAAmB,QAAQ,OAAO,eAAe,CAAC,QAAQ,OAAO,KAAK,KAAK,QAAQ,CAAC,QAAQ,OAAO,eAAe,QAAQ,OAAO,eAAe,KAAK,CAAC,KAAK,eAAe,QAAQ,MAAM;AAE9L,UACE,CAACA,MAAK,SAAQ,KACX,CAAC,SACD,CAAC,eACD,CAAC,oBACD,CAAC,KAAK,OAAO,YAChB;AACA,eAAO;;AAGT,aAAO;IACT;AAsBA,SAAgB,mBAAG,MAAK;AACtB,WAAK,cAAc;IACrB;AAEA,SAAY,eAAG,MAAK;AAElB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;IAChD;AAEA,SAAA,cAAc,CAAC,EAAE,MAAK,MAA6B;;AACjD,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;;AAGF,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,oBAAiB,KAAA,KAAK,QAAQ,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,MAAM,aAAqB,IAAG;AAC1F;;AAGF,WACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,mBAAkB,KAAK,OAAO,KAAK,KAC1C;AACA;;AAGF,WAAK,KAAI;IACX;AAEA,SAAA,mBAAmB,CAAC,UAAqB;AACvC,WAAK,YAAY,EAAE,MAAK,CAAE;IAC5B;AAhDE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,QAAI,YAAY;AACd,WAAK,aAAa;;AAGpB,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACnF,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,SAAK,eAAe;AAEpB,SAAK,QAAQ,OAAM;AACnB,SAAK,QAAQ,MAAM,aAAa;;EAoClC,gBAAa;AACX,UAAM,EAAE,SAAS,cAAa,IAAK,KAAK,OAAO;AAC/C,UAAM,mBAAmB,CAAC,CAAC,cAAc;AAEzC,QAAI,KAAK,SAAS,CAAC,kBAAkB;AACnC;;AAGF,SAAK,QAAQ,kBAAM,eAAe;MAChC,UAAU;MACV,wBAAwB;MACxB,SAAS,KAAK;MACd,aAAa;MACb,SAAS;MACT,WAAW;MACX,aAAa;MACb,GAAG,KAAK;IACT,CAAA;AAGD,QAAI,KAAK,MAAM,OAAO,YAAY;AAC/B,WAAK,MAAM,OAAO,WAA2B,iBAAiB,QAAQ,KAAK,gBAAgB;;;EAIhG,OAAO,MAAkB,UAAsB;;AAC7C,UAAM,EAAE,MAAK,IAAK;AAClB,UAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,UAAM,EAAE,MAAM,GAAE,IAAK;AACrB,UAAM,SAAS,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,SAAS,UAAU,GAAG,SAAS;AAElF,QAAI,QAAQ;AACV;;AAGF,SAAK,cAAa;AAElB,UAAM,cAAa,KAAA,KAAK,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAA;MACnC,QAAQ,KAAK;MACb;MACA;MACA;IACD,CAAA;AAED,QAAI,CAAC,YAAY;AACf,WAAK,KAAI;AAET;;AAGF,KAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;MACnB,0BACE,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,4BAA2B,MAAM,aAAa,MAAM,MAAM,EAAE;IAClF,CAAA;AAED,SAAK,KAAI;;EAGX,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,OAAI;;AACF,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI;;EAGlB,UAAO;;AACL,SAAI,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,YAAY;AAChC,WAAK,MAAM,OAAO,WAA2B,oBAC5C,QACA,KAAK,gBAAgB;;AAGzB,KAAA,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;AACnB,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAI,CAAE;AACtF,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;;AAE3C;AAEY,IAAA,qBAAqB,CAAC,YAAoC;AACrE,SAAO,IAAI,OAAO;IAChB,KACE,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;IACrF,MAAM,UAAQ,IAAI,iBAAiB,EAAE,MAAM,GAAG,QAAO,CAAE;EACxD,CAAA;AACH;ACvNa,IAAA,eAAe,UAAU,OAA4B;EAChE,MAAM;EAEN,aAAU;AACR,WAAO;MACL,SAAS;MACT,cAAc,CAAA;MACd,WAAW;MACX,YAAY;;;EAIhB,wBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAA;;AAGT,WAAO;MACL,mBAAmB;QACjB,WAAW,KAAK,QAAQ;QACxB,QAAQ,KAAK;QACb,SAAS,KAAK,QAAQ;QACtB,cAAc,KAAK,QAAQ;QAC3B,YAAY,KAAK,QAAQ;OAC1B;;;AAGN,CAAA;;;AClCM,IAAMC,cAAa,gBAAgB;EACxC,MAAM;EAEN,OAAO;IACL,WAAW;MACT,MAAM,CAAC,QAAQ,MAAM;MACrB,SAAS;IACV;IAED,QAAQ;MACN,MAAM;MACN,UAAU;IACX;IAED,aAAa;MACX,MAAM;MACN,SAAS;IACV;IAED,cAAc;MACZ,MAAM;MACN,SAAS,OAAO,CAAA;IACjB;IAED,YAAY;MACV,MAAM;MACN,SAAS;IACV;EACF;EAED,MAAM,OAAO,EAAE,MAAK,GAAE;AACpB,UAAM,OAAO,IAAwB,IAAI;AAEzC,cAAU,MAAK;AACb,YAAM,EACJ,aACA,QACA,WACA,YACA,aAAY,IACV;AAEJ,aAAO,eAAe,iBAAiB;QACrC;QACA;QACA,SAAS,KAAK;QACd;QACA;QACA;MACD,CAAA,CAAC;IACJ,CAAC;AAED,oBAAgB,MAAK;AACnB,YAAM,EAAE,WAAW,OAAM,IAAK;AAE9B,aAAO,iBAAiB,SAAS;IACnC,CAAC;AAED,WAAO,MAAM;AAAA,UAAA;AAAA,aAAA,EAAE,OAAO,EAAE,KAAK,KAAI,IAAI,KAAA,MAAM,aAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,KAAA,CAAA;IAAC;;AAE1D,CAAA;AC1DD,SAAS,gBAAmB,OAAQ;AAClC,SAAO,UAAa,CAAC,OAAO,YAAW;AACrC,WAAO;MACL,MAAG;AACD,cAAK;AACL,eAAO;;MAET,IAAI,UAAQ;AAEV,gBAAQ;AAGR,8BAAsB,MAAK;AACzB,gCAAsB,MAAK;AACzB,oBAAO;UACT,CAAC;QACH,CAAC;;;EAGP,CAAC;AACH;AAMM,IAAOC,UAAP,cAAsBC,OAAU;EASpC,YAAY,UAAkC,CAAA,GAAE;AAC9C,UAAM,OAAO;AALR,SAAgB,mBAA4B;AAE5C,SAAU,aAAsB;AAKrC,SAAK,gBAAgB,gBAAgB,KAAK,KAAK,KAAK;AACpD,SAAK,2BAA2B,gBAAgB,KAAK,gBAAgB;AAErE,SAAK,GAAG,qBAAqB,CAAC,EAAE,UAAS,MAAM;AAC7C,WAAK,cAAc,QAAQ;AAC3B,WAAK,yBAAyB,QAAQ,KAAK;IAC7C,CAAC;AAED,WAAO,QAAQ,IAAI;;EAGrB,IAAI,QAAK;AACP,WAAO,KAAK,gBAAgB,KAAK,cAAc,QAAQ,KAAK,KAAK;;EAGnE,IAAI,UAAO;AACT,WAAO,KAAK,2BAA2B,KAAK,yBAAyB,QAAQ,MAAM;;;;;EAM9E,eACL,QACA,eAAkE;AAElE,UAAM,YAAY,MAAM,eAAe,QAAQ,aAAa;AAE5D,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAQ;;AAG7B,WAAO;;;;;EAMF,iBAAiB,iBAAmC;AACzD,UAAM,YAAY,MAAM,iBAAiB,eAAe;AAExD,QAAI,KAAK,iBAAiB,WAAW;AACnC,WAAK,cAAc,QAAQ;;AAG7B,WAAO;;AAEV;AClFM,IAAM,gBAAgB,gBAAgB;EAC3C,MAAM;EAEN,OAAO;IACL,QAAQ;MACN,SAAS;MACT,MAAM;IACP;EACF;EAED,MAAM,OAAK;AACT,UAAM,SAAmC,IAAG;AAC5C,UAAM,WAAW,mBAAkB;AAEnC,gBAAY,MAAK;AACf,YAAM,SAAS,MAAM;AAErB,UAAI,UAAU,OAAO,QAAQ,WAAW,OAAO,OAAO;AACpD,iBAAS,MAAK;AACZ,cAAI,CAAC,OAAO,SAAS,CAAC,OAAO,QAAQ,QAAQ,YAAY;AACvD;;AAGF,gBAAM,UAAU,MAAM,OAAO,KAAK;AAElC,iBAAO,MAAM,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAGxD,iBAAO,mBAAmB,SAAS,IAAI;AAEvC,cAAI,UAAU;AACZ,mBAAO,aAAa;cAClB,GAAG,SAAS;;;;cAIZ,UAAU,SAAS;;;AAIvB,iBAAO,WAAW;YAChB;UACD,CAAA;AAED,iBAAO,gBAAe;QACxB,CAAC;;IAEL,CAAC;AAED,oBAAgB,MAAK;AACnB,YAAM,SAAS,MAAM;AAErB,UAAI,CAAC,QAAQ;AACX;;AAGF,aAAO,mBAAmB;AAC1B,aAAO,aAAa;IACtB,CAAC;AAED,WAAO,EAAE,OAAM;;EAGjB,SAAM;AACJ,WAAO,EACL,OACA;MACE,KAAK,CAAC,OAAW;AAAG,aAAK,SAAS;MAAE;IACrC,CAAA;;AAGN,CAAA;AC5EM,IAAMC,gBAAe,gBAAgB;EAC1C,MAAM;EAEN,OAAO;IACL,WAAW;;;MAGT,MAAM;MACN,SAAS;IACV;IAED,QAAQ;MACN,MAAM;MACN,UAAU;IACX;IAED,cAAc;MACZ,MAAM;MACN,SAAS,OAAO,CAAA;IACjB;IAED,YAAY;MACV,MAAM;MACN,SAAS;IACV;EACF;EAED,MAAM,OAAO,EAAE,MAAK,GAAE;AACpB,UAAM,OAAO,IAAwB,IAAI;AAEzC,cAAU,MAAK;AACb,YAAM,EACJ,WACA,QACA,cACA,WAAU,IACR;AAEJ,aAAO,eAAe,mBAAmB;QACvC;QACA;QACA,SAAS,KAAK;QACd;QACA;MACD,CAAA,CAAC;IACJ,CAAC;AAED,oBAAgB,MAAK;AACnB,YAAM,EAAE,WAAW,OAAM,IAAK;AAE9B,aAAO,iBAAiB,SAAS;IACnC,CAAC;AAED,WAAO,MAAM;AAAA,UAAA;AAAA,aAAA,EAAE,OAAO,EAAE,KAAK,KAAI,IAAI,KAAA,MAAM,aAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,KAAA,CAAA;IAAC;;AAE1D,CAAA;AC/DM,IAAM,kBAAkB,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACV;EACF;EAED,SAAM;AACJ,WAAO,EAAE,KAAK,IAAI;MAChB,OAAO;QACL,YAAY;MACb;MACD,0BAA0B;IAC3B,CAAA;;AAEJ,CAAA;AClBM,IAAM,kBAAkB,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACV;EACF;EAED,QAAQ,CAAC,eAAe,mBAAmB;EAE3C,SAAM;;AACJ,WAAO,EACL,KAAK,IACL;;MAEE,OAAO,KAAK;MACZ,OAAO;QACL,YAAY;MACb;MACD,0BAA0B;;MAE1B,aAAa,KAAK;QAEpB,MAAA,KAAA,KAAK,QAAO,aAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA,CAAA;;AAG5B,CAAA;ICzBY,YAAY,CAAC,UAAkC,CAAA,MAAM;AAChE,QAAM,SAAS,WAAU;AAEzB,YAAU,MAAK;AACb,WAAO,QAAQ,IAAIF,QAAO,OAAO;EACnC,CAAC;AAED,kBAAgB,MAAK;;AAEnB,UAAM,SAAQ,KAAA,OAAO,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ;AACpC,UAAM,QAAQ,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,UAAU,IAAI;AAEnC,KAAA,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,aAAa,OAAO,KAAK;AAE5C,KAAA,KAAA,OAAO,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;EACvB,CAAC;AAED,SAAO;AACT;ICAa,oBAAW;EAWtB,YAAY,WAAsB,EAAE,QAAQ,CAAA,GAAI,OAAM,GAAsB;AAC1E,SAAK,SAAS;AACd,SAAK,YAAY,QAAQ,SAAS;AAClC,SAAK,KAAK,SAAS,cAAc,KAAK;AACtC,SAAK,QAAQ,SAAS,KAAK;AAC3B,SAAK,oBAAoB,KAAK,gBAAe;;EAG/C,IAAI,UAAO;AACT,WAAO,KAAK,kBAAkB;;EAGhC,IAAI,MAAG;;AAEL,SAAI,MAAA,KAAA,KAAK,kBAAkB,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;AACpD,aAAO,KAAK,kBAAkB,MAAM,UAAU;;AAGhD,YAAO,MAAA,KAAA,KAAK,kBAAkB,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,eAAW,QAAA,OAAA,SAAA,SAAA,GAAA;;EAGlD,kBAAe;AACb,QAAI,QAAuB,EAAE,KAAK,WAA8B,KAAK,KAAK;AAE1E,QAAI,KAAK,OAAO,YAAY;AAC1B,YAAM,aAAa,KAAK,OAAO;;AAEjC,QAAI,OAAO,aAAa,eAAe,KAAK,IAAI;AAC9C,aAAO,OAAO,KAAK,EAAE;;AAGvB,UAAM,UAAU,MAAK;AACnB,UAAI,KAAK,IAAI;AACX,eAAO,MAAM,KAAK,EAAE;;AAEtB,WAAK,KAAK;AACV,cAAQ;IACV;AAEA,WAAO,EAAE,OAAO,SAAS,IAAI,KAAK,KAAK,KAAK,GAAG,oBAAoB,KAAI;;EAGzE,YAAY,QAA6B,CAAA,GAAE;AACzC,WAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AAC7C,WAAK,MAAM,GAAG,IAAI;IACpB,CAAC;AACD,SAAK,gBAAe;;EAGtB,UAAO;AACL,SAAK,kBAAkB,QAAO;;AAEjC;ACrEY,IAAA,gBAAgB;EAC3B,QAAQ;IACN,MAAM;IACN,UAAU;EACX;EACD,MAAM;IACJ,MAAM;IACN,UAAU;EACX;EACD,aAAa;IACX,MAAM;IACN,UAAU;EACX;EACD,UAAU;IACR,MAAM;IACN,UAAU;EACX;EACD,WAAW;IACT,MAAM;IACN,UAAU;EACX;EACD,QAAQ;IACN,MAAM;IACN,UAAU;EACX;EACD,kBAAkB;IAChB,MAAM;IACN,UAAU;EACX;EACD,YAAY;IACV,MAAM;IACN,UAAU;EACX;EACD,MAAM;IACJ,MAAM;IACN,UAAU;EACX;EACD,kBAAkB;IAChB,MAAM;IACN,UAAU;EACX;EACD,gBAAgB;IACd,MAAM;IACN,UAAU;EACX;;AAiBH,IAAM,cAAN,cAA0B,SAAuD;EAK/E,QAAK;AACH,UAAM,QAAQ;MACZ,QAAQ,KAAK;MACb,MAAM,KAAK;MACX,aAAa,KAAK;MAClB,kBAAkB,KAAK;MACvB,MAAM,KAAK;MACX,UAAU;MACV,WAAW,KAAK;MAChB,gBAAgB,KAAK;MACrB,QAAQ,MAAM,KAAK,OAAM;MACzB,kBAAkB,CAAC,aAAa,CAAA,MAAO,KAAK,iBAAiB,UAAU;MACvE,YAAY,MAAM,KAAK,WAAU;;AAGnC,UAAM,cAAc,KAAK,YAAY,KAAK,IAAI;AAE9C,SAAK,oBAAoB,IAAI,KAAK,qBAAoB,CAAE;AAExD,UAAM,oBAAoB,gBAAgB;MACxC,SAAS,EAAE,GAAG,KAAK,UAAS;MAC5B,OAAO,OAAO,KAAK,KAAK;MACxB,UAAW,KAAK,UAAkB;MAClC,OAAO,mBAAgB;;AACrB,gBAAQ,eAAe,WAAW;AAClC,gBAAQ,qBAAqB,KAAK,iBAAiB;AAEnD,gBAAO,MAAA,KAAC,KAAK,WAAkB,WAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA,eAAe;UACpD,QAAQ,MAAM;QACf,CAAA;;;;;MAKH,WAAW,KAAK,UAAU;;;;MAI1B,cAAc,KAAK,UAAU;;;;MAI7B,QAAQ,KAAK,UAAU;;;MAGvB,QAAQ,KAAK,UAAU;IACxB,CAAA;AAED,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,OAAO,GAAG,mBAAmB,KAAK,qBAAqB;AAE5D,SAAK,WAAW,IAAI,YAAY,mBAAmB;MACjD,QAAQ,KAAK;MACb;IACD,CAAA;;;;;;EAOH,IAAI,MAAG;AACL,QAAI,CAAC,KAAK,SAAS,WAAW,CAAC,KAAK,SAAS,QAAQ,aAAa,wBAAwB,GAAG;AAC3F,YAAM,MAAM,8DAA8D;;AAG5E,WAAO,KAAK,SAAS;;;;;;EAOvB,IAAI,aAAU;AACZ,QAAI,KAAK,KAAK,QAAQ;AACpB,aAAO;;AAGT,WAAO,KAAK,IAAI,cAAc,0BAA0B;;;;;;EAO1D,wBAAqB;AACnB,UAAM,EAAE,MAAM,GAAE,IAAK,KAAK,OAAO,MAAM;AACvC,UAAM,MAAM,KAAK,OAAM;AAEvB,QAAI,OAAO,QAAQ,UAAU;AAC3B;;AAGF,QAAI,QAAQ,OAAO,MAAM,MAAM,KAAK,KAAK,UAAU;AACjD,UAAI,KAAK,SAAS,MAAM,UAAU;AAChC;;AAGF,WAAK,WAAU;WACV;AACL,UAAI,CAAC,KAAK,SAAS,MAAM,UAAU;AACjC;;AAGF,WAAK,aAAY;;;;;;;EAQrB,OACE,MACA,aACA,kBAAkC;AAElC,UAAM,oBAAoB,CAAC,UAA+B;AACxD,WAAK,kBAAkB,QAAQ,KAAK,qBAAoB;AACxD,WAAK,SAAS,YAAY,KAAK;IACjC;AAEA,QAAI,OAAO,KAAK,QAAQ,WAAW,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,sBAAsB,KAAK;AAEjC,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAExB,aAAO,KAAK,QAAQ,OAAO;QACzB;QACA;QACA,SAAS;QACT,gBAAgB;QAChB;QACA;QACA,aAAa,MAAM,kBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;MAC7E,CAAA;;AAGH,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;;AAGT,QAAI,SAAS,KAAK,QAAQ,KAAK,gBAAgB,eAAe,KAAK,qBAAqB,kBAAkB;AACxG,aAAO;;AAGT,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,sBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;AAEzD,WAAO;;;;;;EAOT,aAAU;AACR,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,IAAI,0BAA0B;;;;;;;EAQlE,eAAY;AACV,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,OAAO,0BAA0B;;;EAIrE,uBAAoB;AAClB,WACE,KAAK,YAEF,IAAI,UAAQ,KAAK,KAAK,MAAM,KAAK,EACjC,KAAI,EACJ,KAAK,GAAG;;EAIf,UAAO;AACL,SAAK,SAAS,QAAO;AACrB,SAAK,OAAO,IAAI,mBAAmB,KAAK,qBAAqB;;AAEhE;AAEe,SAAA,oBACd,WACA,SAA6C;AAE7C,SAAO,WAAQ;AAIb,QAAI,CAAE,MAAM,OAAkB,kBAAkB;AAC9C,aAAO,CAAA;;AAGT,UAAM,sBAAsB,OAAO,cAAc,cAAc,eAAe,YACzE,UAAU,YACX;AAEJ,WAAO,IAAI,YAAY,qBAAqB,OAAO,OAAO;EAC5D;AACF;", "names": ["BOX_CLASS", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "document", "body", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "createElement", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "actualContains", "parent", "child", "target", "contains", "getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "window", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "isIE11", "msCrypto", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "innerHTML", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getDocument", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "getC<PERSON>rentTarget", "parentNode", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "cleanupInteractiveMouseListeners", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "doc", "onTransitionedOut", "callback", "onTransitionEnd", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "applyStylesModifier", "applyStyles", "effect", "state", "initialStyles", "popper", "position", "options", "strategy", "left", "top", "margin", "arrow", "reference", "Object", "assign", "elements", "style", "styles", "tippy", "setDefaultProps", "render", "view", "shouldShow", "view", "BubbleMenu", "Editor", "CoreEditor", "FloatingMenu"]}