<template>
  <div class="document-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-left">
        <h3 class="list-title">
          {{ currentCategory ? currentCategory.name : '全部文档' }}
        </h3>
        <span class="document-count">{{ filteredDocuments.length }} 篇文档</span>
      </div>
      
      <div class="header-right">
        <el-select
          v-model="sortBy"
          placeholder="排序方式"
          size="small"
          style="width: 120px"
        >
          <el-option label="最新更新" value="updatedAt" />
          <el-option label="创建时间" value="createdAt" />
          <el-option label="浏览次数" value="viewCount" />
          <el-option label="点赞数量" value="likeCount" />
          <el-option label="标题" value="title" />
        </el-select>
        
        <el-button
          :icon="viewMode === 'list' ? Grid : List"
          size="small"
          @click="toggleViewMode"
        />
      </div>
    </div>

    <!-- 文档列表 -->
    <div class="documents-container" v-loading="loading">
      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="list-view">
        <div
          v-for="document in sortedDocuments"
          :key="document.id"
          class="document-item"
          @click="handleDocumentClick(document)"
        >
          <div class="document-content">
            <div class="document-header">
              <h4 class="document-title">{{ document.title }}</h4>
              <div class="document-status">
                <el-tag
                  :type="getStatusType(document.status)"
                  size="small"
                >
                  {{ getStatusText(document.status) }}
                </el-tag>
              </div>
            </div>
            
            <p class="document-summary">{{ document.summary }}</p>
            
            <div class="document-tags">
              <el-tag
                v-for="tag in document.tags"
                :key="tag"
                size="small"
                effect="plain"
                class="tag"
              >
                {{ tag }}
              </el-tag>
            </div>
            
            <div class="document-meta">
              <div class="meta-left">
                <span class="author">{{ document.author }}</span>
                <span class="separator">·</span>
                <span class="time">{{ formatTime(document.updatedAt) }}</span>
              </div>
              
              <div class="meta-right">
                <span class="stat">
                  <el-icon><View /></el-icon>
                  {{ document.viewCount }}
                </span>
                <span class="stat">
                  <el-icon><Star /></el-icon>
                  {{ document.likeCount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div
          v-for="document in sortedDocuments"
          :key="document.id"
          class="document-card"
          @click="handleDocumentClick(document)"
        >
          <div class="card-header">
            <h4 class="card-title">{{ document.title }}</h4>
            <el-tag
              :type="getStatusType(document.status)"
              size="small"
            >
              {{ getStatusText(document.status) }}
            </el-tag>
          </div>
          
          <p class="card-summary">{{ document.summary }}</p>
          
          <div class="card-tags">
            <el-tag
              v-for="tag in document.tags.slice(0, 3)"
              :key="tag"
              size="small"
              effect="plain"
              class="tag"
            >
              {{ tag }}
            </el-tag>
            <span v-if="document.tags.length > 3" class="more-tags">
              +{{ document.tags.length - 3 }}
            </span>
          </div>
          
          <div class="card-footer">
            <div class="footer-left">
              <span class="author">{{ document.author }}</span>
              <span class="time">{{ formatTime(document.updatedAt) }}</span>
            </div>
            
            <div class="footer-right">
              <span class="stat">
                <el-icon><View /></el-icon>
                {{ document.viewCount }}
              </span>
              <span class="stat">
                <el-icon><Star /></el-icon>
                {{ document.likeCount }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredDocuments.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无文档">
          <el-button type="primary" @click="handleCreateDocument">
            创建第一篇文档
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { View, Star, Grid, List } from '@element-plus/icons-vue'
import type { KnowledgeDocument, KnowledgeCategory } from '@/types/knowledge'

interface Props {
  documents: KnowledgeDocument[]
  currentCategory?: KnowledgeCategory | null
  loading?: boolean
}

interface Emits {
  (e: 'document-click', document: KnowledgeDocument): void
  (e: 'create-document'): void
}

const props = withDefaults(defineProps<Props>(), {
  documents: () => [],
  currentCategory: null,
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const sortBy = ref('updatedAt')
const viewMode = ref<'list' | 'card'>('list')

// 计算属性
const filteredDocuments = computed(() => {
  if (!props.currentCategory) {
    return props.documents
  }
  
  return props.documents.filter(doc => {
    // 如果当前分类有子分类，也包含子分类的文档
    if (props.currentCategory!.children && props.currentCategory!.children.length > 0) {
      const childIds = props.currentCategory!.children.map(child => child.id)
      return doc.categoryId === props.currentCategory!.id || childIds.includes(doc.categoryId)
    }
    
    return doc.categoryId === props.currentCategory!.id
  })
})

const sortedDocuments = computed(() => {
  const docs = [...filteredDocuments.value]
  
  return docs.sort((a, b) => {
    switch (sortBy.value) {
      case 'title':
        return a.title.localeCompare(b.title)
      case 'createdAt':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case 'viewCount':
        return b.viewCount - a.viewCount
      case 'likeCount':
        return b.likeCount - a.likeCount
      case 'updatedAt':
      default:
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    }
  })
})

// 方法
const handleDocumentClick = (document: KnowledgeDocument) => {
  emit('document-click', document)
}

const handleCreateDocument = () => {
  emit('create-document')
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'list' ? 'card' : 'list'
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - new Date(date).getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    if (hours === 0) {
      const minutes = Math.floor(diff / (1000 * 60))
      return `${minutes}分钟前`
    }
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return new Date(date).toLocaleDateString()
  }
}
</script>

<style scoped>
.document-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
}

.header-left {
  display: flex;
  align-items: baseline;
  gap: 12px;
}

.list-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.document-count {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.documents-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

/* 列表视图样式 */
.list-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.document-item {
  padding: 20px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--el-bg-color);
}

.document-item:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.document-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  flex: 1;
}

.document-summary {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.document-tags {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  font-size: 12px;
}

.document-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.meta-left {
  display: flex;
  align-items: center;
  gap: 6px;
}

.separator {
  color: var(--el-text-color-disabled);
}

.meta-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 卡片视图样式 */
.card-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.document-card {
  padding: 20px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--el-bg-color);
  height: fit-content;
}

.document-card:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  flex: 1;
  margin-right: 12px;
}

.card-summary {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-tags {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.more-tags {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
