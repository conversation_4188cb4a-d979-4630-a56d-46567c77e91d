<template>
  <div class="knowledge-base">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <h1 class="app-title">
          <el-icon><Notebook /></el-icon>
          知识库管理
        </h1>
      </div>
      
      <div class="navbar-right">
        <el-button type="primary" :icon="Plus" @click="handleCreateDocument">
          新建文档
        </el-button>
        <el-button :icon="Setting" @click="showSettings = true">
          设置
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 -->
      <div class="sidebar-container">
        <KnowledgeSidebar
          @create-document="handleCreateDocument"
          @create-category="handleCreateCategory"
          @category-selected="handleCategorySelected"
          @document-selected="handleDocumentSelected"
          @search="handleSearch"
        />
      </div>

      <!-- 内容区域 -->
      <div class="content-container">
        <!-- 文档列表视图 -->
        <div v-if="currentView === 'list'" class="list-view">
          <DocumentList
            :documents="displayDocuments"
            :current-category="currentCategory"
            :loading="loading"
            @document-click="handleDocumentSelected"
            @create-document="handleCreateDocument"
          />
        </div>

        <!-- 文档详情视图 -->
        <div v-else-if="currentView === 'detail'" class="detail-view">
          <DocumentDetail
            :document="currentDocument"
            @back="handleBackToList"
            @edit="handleEditDocument"
            @delete="handleDeleteDocument"
          />
        </div>

        <!-- 文档编辑视图 -->
        <div v-else-if="currentView === 'edit'" class="edit-view">
          <DocumentEditor
            :document="editingDocument"
            :categories="knowledgeStore.categories"
            @save="handleSaveDocument"
            @cancel="handleCancelEdit"
          />
        </div>

        <!-- 仪表板视图 -->
        <div v-else class="dashboard-view">
          <KnowledgeDashboard
            :stats="dashboardStats"
            @document-click="handleDocumentSelected"
            @category-click="handleCategorySelected"
          />
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="设置"
      width="600px"
    >
      <div class="settings-content">
        <el-form label-width="120px">
          <el-form-item label="主题模式">
            <el-radio-group v-model="themeMode">
              <el-radio value="light">浅色</el-radio>
              <el-radio value="dark">深色</el-radio>
              <el-radio value="auto">跟随系统</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="默认视图">
            <el-radio-group v-model="defaultView">
              <el-radio value="dashboard">仪表板</el-radio>
              <el-radio value="list">文档列表</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="自动保存">
            <el-switch v-model="autoSave" />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Notebook, Plus, Setting } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useKnowledgeStore } from '@/stores/counter'
import KnowledgeSidebar from '@/components/KnowledgeSidebar.vue'
import DocumentList from '@/components/DocumentList.vue'
import DocumentDetail from '@/components/DocumentDetail.vue'
import DocumentEditor from '@/components/DocumentEditor.vue'
import KnowledgeDashboard from '@/components/KnowledgeDashboard.vue'
import type { KnowledgeDocument, KnowledgeCategory } from '@/types/knowledge'

// 状态管理
const knowledgeStore = useKnowledgeStore()

// 响应式数据
const currentView = ref<'dashboard' | 'list' | 'detail' | 'edit'>('dashboard')
const currentDocument = ref<KnowledgeDocument | null>(null)
const currentCategory = ref<KnowledgeCategory | null>(null)
const editingDocument = ref<KnowledgeDocument | null>(null)
const loading = ref(false)
const showSettings = ref(false)

// 设置相关
const themeMode = ref('light')
const defaultView = ref('dashboard')
const autoSave = ref(true)

// 搜索相关
const searchQuery = ref('')
const searchTags = ref<string[]>([])

// 计算属性
const displayDocuments = computed(() => {
  if (knowledgeStore.searchResults) {
    return knowledgeStore.searchResults.documents
  }
  
  if (currentCategory.value) {
    const categoryDocs = knowledgeStore.documentsByCategory.get(currentCategory.value.id) || []
    
    // 如果是父分类，也包含子分类的文档
    if (currentCategory.value.children && currentCategory.value.children.length > 0) {
      const childDocs = currentCategory.value.children.flatMap(child => 
        knowledgeStore.documentsByCategory.get(child.id) || []
      )
      return [...categoryDocs, ...childDocs]
    }
    
    return categoryDocs
  }
  
  return knowledgeStore.documents
})

const dashboardStats = computed(() => ({
  totalDocuments: knowledgeStore.documents.length,
  totalCategories: knowledgeStore.categories.length,
  totalTags: knowledgeStore.tags.length,
  recentDocuments: knowledgeStore.recentDocuments,
  popularDocuments: knowledgeStore.popularDocuments
}))

// 方法
const handleCreateDocument = () => {
  editingDocument.value = {
    id: '',
    title: '新建文档',
    content: '',
    summary: '',
    categoryId: currentCategory.value?.id || knowledgeStore.categories[0]?.id || '',
    tags: [],
    author: '当前用户',
    status: 'draft',
    isPublic: false,
    viewCount: 0,
    likeCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastEditedBy: '当前用户'
  }
  currentView.value = 'edit'
}

const handleCreateCategory = () => {
  ElMessage.info('创建分类功能开发中...')
}

const handleCategorySelected = (category: KnowledgeCategory | null) => {
  currentCategory.value = category
  currentView.value = 'list'
  // 清除搜索结果
  knowledgeStore.searchResults = null
}

const handleDocumentSelected = (document: KnowledgeDocument) => {
  currentDocument.value = document
  knowledgeStore.setCurrentDocument(document)
  currentView.value = 'detail'
}

const handleBackToList = () => {
  currentView.value = 'list'
  currentDocument.value = null
}

const handleEditDocument = (document: KnowledgeDocument) => {
  editingDocument.value = { ...document }
  currentView.value = 'edit'
}

const handleDeleteDocument = (document: KnowledgeDocument) => {
  ElMessage.info('删除文档功能开发中...')
}

const handleSaveDocument = (document: KnowledgeDocument) => {
  if (!document.id) {
    // 新建文档
    document.id = Date.now().toString()
    document.createdAt = new Date()
    knowledgeStore.documents.push(document)
    ElMessage.success('文档创建成功')
  } else {
    // 更新文档
    const index = knowledgeStore.documents.findIndex(d => d.id === document.id)
    if (index > -1) {
      document.updatedAt = new Date()
      knowledgeStore.documents[index] = document
      ElMessage.success('文档保存成功')
    }
  }
  
  currentDocument.value = document
  currentView.value = 'detail'
}

const handleCancelEdit = () => {
  if (currentDocument.value) {
    currentView.value = 'detail'
  } else {
    currentView.value = 'list'
  }
  editingDocument.value = null
}

const handleSearch = (query: string, tags: string[]) => {
  searchQuery.value = query
  searchTags.value = tags
  
  if (query || tags.length > 0) {
    knowledgeStore.searchDocuments(query, currentCategory.value?.id)
    currentView.value = 'list'
  } else {
    knowledgeStore.searchResults = null
  }
}

const saveSettings = () => {
  // 保存设置到本地存储
  localStorage.setItem('knowledge-settings', JSON.stringify({
    themeMode: themeMode.value,
    defaultView: defaultView.value,
    autoSave: autoSave.value
  }))
  
  showSettings.value = false
  ElMessage.success('设置已保存')
}

const loadSettings = () => {
  const settings = localStorage.getItem('knowledge-settings')
  if (settings) {
    const parsed = JSON.parse(settings)
    themeMode.value = parsed.themeMode || 'light'
    defaultView.value = parsed.defaultView || 'dashboard'
    autoSave.value = parsed.autoSave !== false
  }
}

// 生命周期
onMounted(() => {
  knowledgeStore.initializeData()
  loadSettings()
  currentView.value = defaultView.value as any
})
</script>

<style scoped>
.knowledge-base {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
}

.top-navbar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.navbar-left {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar-container {
  width: 280px;
  flex-shrink: 0;
}

.content-container {
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

.list-view,
.detail-view,
.edit-view,
.dashboard-view {
  height: 100%;
}

.settings-content {
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .sidebar-container {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color);
  }
  
  .top-navbar {
    padding: 0 16px;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .navbar-right {
    gap: 8px;
  }
}
</style>
