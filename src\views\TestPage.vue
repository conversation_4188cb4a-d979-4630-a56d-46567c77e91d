<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>如果你能看到这个页面，说明Vue应用基本运行正常。</p>
    
    <el-button type="primary" @click="showMessage">点击测试</el-button>
    
    <div style="margin-top: 20px;">
      <h2>Element Plus 组件测试</h2>
      <el-card>
        <template #header>
          <span>卡片标题</span>
        </template>
        <p>这是一个Element Plus卡片组件</p>
      </el-card>
    </div>
    
    <div style="margin-top: 20px;">
      <h2>图标测试</h2>
      <el-icon><Plus /></el-icon>
      <el-icon><Setting /></el-icon>
      <el-icon><Document /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Plus, Setting, Document } from '@element-plus/icons-vue'

const showMessage = () => {
  ElMessage.success('Element Plus 工作正常！')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}
</style>
