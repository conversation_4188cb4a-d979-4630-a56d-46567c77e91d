<template>
  <div class="knowledge-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h2 class="welcome-title">欢迎回来！</h2>
        <p class="welcome-subtitle">继续构建你的知识库</p>
      </div>
      
      <div class="welcome-actions">
        <el-button type="primary" :icon="Plus" size="large" @click="handleCreateDocument">
          新建文档
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon document-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalDocuments }}</div>
            <div class="stat-label">总文档数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon category-icon">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalCategories }}</div>
            <div class="stat-label">分类数量</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon tag-icon">
            <el-icon><CollectionTag /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalTags }}</div>
            <div class="stat-label">标签数量</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon view-icon">
            <el-icon><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ totalViews }}</div>
            <div class="stat-label">总浏览量</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <div class="content-grid">
        <!-- 最近文档 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Clock /></el-icon>
              最近编辑
            </h3>
            <el-button text type="primary" @click="handleViewAllRecent">
              查看全部
            </el-button>
          </div>
          
          <div class="card-content">
            <div v-if="stats.recentDocuments.length === 0" class="empty-state">
              <el-empty description="暂无最近文档" :image-size="80" />
            </div>
            
            <div v-else class="document-list">
              <div
                v-for="doc in stats.recentDocuments.slice(0, 5)"
                :key="doc.id"
                class="document-item"
                @click="handleDocumentClick(doc)"
              >
                <div class="document-info">
                  <h4 class="document-title">{{ doc.title }}</h4>
                  <p class="document-summary">{{ doc.summary || '暂无摘要' }}</p>
                  <div class="document-meta">
                    <span class="author">{{ doc.author }}</span>
                    <span class="time">{{ formatTime(doc.updatedAt) }}</span>
                  </div>
                </div>
                
                <div class="document-status">
                  <el-tag :type="getStatusType(doc.status)" size="small">
                    {{ getStatusText(doc.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 热门文档 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><TrendCharts /></el-icon>
              热门文档
            </h3>
            <el-button text type="primary" @click="handleViewAllPopular">
              查看全部
            </el-button>
          </div>
          
          <div class="card-content">
            <div v-if="stats.popularDocuments.length === 0" class="empty-state">
              <el-empty description="暂无热门文档" :image-size="80" />
            </div>
            
            <div v-else class="document-list">
              <div
                v-for="(doc, index) in stats.popularDocuments.slice(0, 5)"
                :key="doc.id"
                class="document-item popular-item"
                @click="handleDocumentClick(doc)"
              >
                <div class="rank-badge">{{ index + 1 }}</div>
                
                <div class="document-info">
                  <h4 class="document-title">{{ doc.title }}</h4>
                  <div class="document-stats">
                    <span class="stat">
                      <el-icon><View /></el-icon>
                      {{ doc.viewCount }}
                    </span>
                    <span class="stat">
                      <el-icon><Star /></el-icon>
                      {{ doc.likeCount }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <h3 class="section-title">快速操作</h3>
      
      <div class="actions-grid">
        <div class="action-card" @click="handleCreateDocument">
          <div class="action-icon">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="action-content">
            <h4 class="action-title">新建文档</h4>
            <p class="action-description">创建一篇新的知识文档</p>
          </div>
        </div>
        
        <div class="action-card" @click="handleCreateCategory">
          <div class="action-icon">
            <el-icon><FolderAdd /></el-icon>
          </div>
          <div class="action-content">
            <h4 class="action-title">新建分类</h4>
            <p class="action-description">创建新的文档分类</p>
          </div>
        </div>
        
        <div class="action-card" @click="handleImportDocuments">
          <div class="action-icon">
            <el-icon><Upload /></el-icon>
          </div>
          <div class="action-content">
            <h4 class="action-title">导入文档</h4>
            <p class="action-description">批量导入已有文档</p>
          </div>
        </div>
        
        <div class="action-card" @click="handleExportAll">
          <div class="action-icon">
            <el-icon><Download /></el-icon>
          </div>
          <div class="action-content">
            <h4 class="action-title">导出备份</h4>
            <p class="action-description">导出所有文档数据</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Plus,
  Document,
  Folder,
  CollectionTag,
  View,
  Clock,
  TrendCharts,
  Star,
  EditPen,
  FolderAdd,
  Upload,
  Download
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { KnowledgeStats, KnowledgeDocument } from '@/types/knowledge'

interface Props {
  stats: KnowledgeStats
}

interface Emits {
  (e: 'document-click', document: KnowledgeDocument): void
  (e: 'category-click'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const totalViews = computed(() => {
  return props.stats.recentDocuments.reduce((total, doc) => total + doc.viewCount, 0)
})

// 方法
const handleCreateDocument = () => {
  ElMessage.info('创建文档功能将在主页面实现')
}

const handleCreateCategory = () => {
  ElMessage.info('创建分类功能开发中...')
}

const handleImportDocuments = () => {
  ElMessage.info('导入文档功能开发中...')
}

const handleExportAll = () => {
  ElMessage.info('导出备份功能开发中...')
}

const handleDocumentClick = (document: KnowledgeDocument) => {
  emit('document-click', document)
}

const handleViewAllRecent = () => {
  ElMessage.info('查看全部最近文档')
}

const handleViewAllPopular = () => {
  ElMessage.info('查看全部热门文档')
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - new Date(date).getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    if (hours === 0) {
      const minutes = Math.floor(diff / (1000 * 60))
      return `${minutes}分钟前`
    }
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return new Date(date).toLocaleDateString()
  }
}
</script>

<style scoped>
.knowledge-dashboard {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
  background-color: var(--el-bg-color-page);
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
  border-radius: 12px;
  border: 1px solid var(--el-color-primary-light-7);
}

.welcome-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.welcome-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background-color: var(--el-bg-color);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.document-icon {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.category-icon {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.tag-icon {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.view-icon {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.content-section {
  margin-bottom: 32px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.content-card {
  background-color: var(--el-bg-color);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  padding: 16px 24px 24px;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: all 0.2s;
}

.document-item:hover {
  border-color: var(--el-color-primary-light-7);
  background-color: var(--el-fill-color-light);
}

.popular-item {
  align-items: center;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
}

.document-info {
  flex: 1;
}

.document-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.document-summary {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.document-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.document-stats {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.quick-actions-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 12px;
  border: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: all 0.3s;
}

.action-card:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.action-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.action-description {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .knowledge-dashboard {
    padding: 16px;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 24px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
    margin-right: 12px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .card-header {
    padding: 16px 20px;
  }
  
  .card-content {
    padding: 12px 20px 20px;
  }
}
</style>
