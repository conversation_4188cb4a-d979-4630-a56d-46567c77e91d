{"name": "kowndoc", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tiptap/extension-code-block-lowlight": "^2.13.0", "@tiptap/extension-highlight": "^2.13.0", "@tiptap/extension-image": "^2.13.0", "@tiptap/extension-link": "^2.13.0", "@tiptap/extension-table": "^2.13.0", "@tiptap/extension-task-item": "^2.13.0", "@tiptap/extension-task-list": "^2.13.0", "@tiptap/starter-kit": "^2.13.0", "@tiptap/vue-3": "^2.13.0", "element-plus": "^2.9.11", "lowlight": "^3.3.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}