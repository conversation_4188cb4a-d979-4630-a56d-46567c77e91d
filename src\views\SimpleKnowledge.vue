<template>
  <div class="simple-knowledge">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <h1 class="app-title">
          <el-icon><Notebook /></el-icon>
          知识库管理
        </h1>
      </div>
      
      <div class="navbar-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          新建文档
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div class="search-section">
          <el-input
            v-model="searchQuery"
            placeholder="搜索文档..."
            :prefix-icon="Search"
            clearable
          />
        </div>
        
        <div class="categories-section">
          <h3>文档分类</h3>
          <div class="category-list">
            <div 
              v-for="category in categories" 
              :key="category.id"
              class="category-item"
              :class="{ active: selectedCategory?.id === category.id }"
              @click="selectCategory(category)"
            >
              <el-icon><Folder /></el-icon>
              {{ category.name }}
              <span class="count">({{ getDocumentCount(category.id) }})</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 文档列表 -->
        <div v-if="currentView === 'list'" class="document-list">
          <div class="list-header">
            <h2>{{ selectedCategory ? selectedCategory.name : '全部文档' }}</h2>
            <span class="count">{{ filteredDocuments.length }} 篇文档</span>
          </div>
          
          <div class="documents">
            <div 
              v-for="doc in filteredDocuments" 
              :key="doc.id"
              class="document-item"
              @click="viewDocument(doc)"
            >
              <h3>{{ doc.title }}</h3>
              <p>{{ doc.summary || '暂无摘要' }}</p>
              <div class="meta">
                <span>{{ doc.author }}</span>
                <span>{{ formatDate(doc.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 文档详情 -->
        <div v-else-if="currentView === 'detail'" class="document-detail">
          <div class="detail-header">
            <el-button :icon="ArrowLeft" @click="backToList">返回</el-button>
            <el-button type="primary" @click="editDocument">编辑</el-button>
          </div>
          
          <div class="detail-content">
            <h1>{{ currentDocument?.title }}</h1>
            <div class="meta">
              <span>作者：{{ currentDocument?.author }}</span>
              <span>更新时间：{{ formatDate(currentDocument?.updatedAt) }}</span>
            </div>
            <div class="content" v-html="currentDocument?.content"></div>
          </div>
        </div>

        <!-- 文档编辑 -->
        <div v-else-if="currentView === 'edit'" class="document-edit">
          <div class="edit-header">
            <el-button :icon="ArrowLeft" @click="cancelEdit">取消</el-button>
            <el-button type="primary" @click="saveDocument">保存</el-button>
          </div>
          
          <div class="edit-content">
            <el-input 
              v-model="editForm.title" 
              placeholder="请输入文档标题"
              size="large"
              style="margin-bottom: 20px;"
            />
            
            <SimpleEditor 
              v-model="editForm.content"
              placeholder="开始编写文档内容..."
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新建文档对话框 -->
    <el-dialog v-model="showCreateDialog" title="新建文档" width="500px">
      <el-form :model="createForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="createForm.title" placeholder="请输入文档标题" />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="createForm.categoryId" placeholder="选择分类">
            <el-option 
              v-for="category in categories" 
              :key="category.id"
              :label="category.name" 
              :value="category.id" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createDocument">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Notebook, 
  Plus, 
  Search, 
  Folder, 
  ArrowLeft 
} from '@element-plus/icons-vue'
import SimpleEditor from '@/components/SimpleEditor.vue'

// 类型定义
interface Document {
  id: string
  title: string
  content: string
  summary: string
  categoryId: string
  author: string
  createdAt: Date
  updatedAt: Date
}

interface Category {
  id: string
  name: string
}

// 响应式数据
const currentView = ref<'list' | 'detail' | 'edit'>('list')
const searchQuery = ref('')
const selectedCategory = ref<Category | null>(null)
const currentDocument = ref<Document | null>(null)
const showCreateDialog = ref(false)

// 表单数据
const editForm = ref({
  title: '',
  content: '',
  categoryId: ''
})

const createForm = ref({
  title: '',
  categoryId: ''
})

// 示例数据
const categories = ref<Category[]>([
  { id: '1', name: '技术文档' },
  { id: '2', name: '产品文档' },
  { id: '3', name: '运维手册' }
])

const documents = ref<Document[]>([
  {
    id: '1',
    title: 'Vue 3 开发指南',
    content: '<h1>Vue 3 开发指南</h1><p>这是一个关于Vue 3开发的详细指南...</p>',
    summary: 'Vue 3 框架的完整开发指南',
    categoryId: '1',
    author: '张三',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '2',
    title: '产品需求文档',
    content: '<h1>产品需求文档</h1><p>这是产品的详细需求说明...</p>',
    summary: '产品功能需求的详细说明',
    categoryId: '2',
    author: '李四',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  }
])

// 计算属性
const filteredDocuments = computed(() => {
  let filtered = documents.value

  if (selectedCategory.value) {
    filtered = filtered.filter(doc => doc.categoryId === selectedCategory.value!.id)
  }

  if (searchQuery.value) {
    filtered = filtered.filter(doc => 
      doc.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      doc.content.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  return filtered
})

// 方法
const selectCategory = (category: Category) => {
  selectedCategory.value = selectedCategory.value?.id === category.id ? null : category
}

const getDocumentCount = (categoryId: string) => {
  return documents.value.filter(doc => doc.categoryId === categoryId).length
}

const viewDocument = (doc: Document) => {
  currentDocument.value = doc
  currentView.value = 'detail'
}

const backToList = () => {
  currentView.value = 'list'
  currentDocument.value = null
}

const editDocument = () => {
  if (currentDocument.value) {
    editForm.value = {
      title: currentDocument.value.title,
      content: currentDocument.value.content,
      categoryId: currentDocument.value.categoryId
    }
    currentView.value = 'edit'
  }
}

const cancelEdit = () => {
  if (currentDocument.value) {
    currentView.value = 'detail'
  } else {
    currentView.value = 'list'
  }
}

const saveDocument = () => {
  if (currentDocument.value) {
    // 更新现有文档
    const index = documents.value.findIndex(d => d.id === currentDocument.value!.id)
    if (index > -1) {
      documents.value[index] = {
        ...documents.value[index],
        title: editForm.value.title,
        content: editForm.value.content,
        categoryId: editForm.value.categoryId,
        updatedAt: new Date()
      }
      currentDocument.value = documents.value[index]
    }
  }
  
  currentView.value = 'detail'
  ElMessage.success('文档保存成功')
}

const createDocument = () => {
  if (!createForm.value.title || !createForm.value.categoryId) {
    ElMessage.warning('请填写完整信息')
    return
  }

  const newDoc: Document = {
    id: Date.now().toString(),
    title: createForm.value.title,
    content: '<p>开始编写文档内容...</p>',
    summary: '',
    categoryId: createForm.value.categoryId,
    author: '当前用户',
    createdAt: new Date(),
    updatedAt: new Date()
  }

  documents.value.push(newDoc)
  showCreateDialog.value = false
  createForm.value = { title: '', categoryId: '' }
  
  // 直接进入编辑模式
  currentDocument.value = newDoc
  editForm.value = {
    title: newDoc.title,
    content: newDoc.content,
    categoryId: newDoc.categoryId
  }
  currentView.value = 'edit'
  
  ElMessage.success('文档创建成功')
}

const formatDate = (date: Date | undefined) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  ElMessage.success('知识库系统加载成功！')
})
</script>

<style scoped>
.simple-knowledge {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.top-navbar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 280px;
  background-color: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color);
  padding: 20px;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 24px;
}

.categories-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.category-item:hover {
  background-color: var(--el-fill-color-light);
}

.category-item.active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.count {
  margin-left: auto;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.content-area {
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

.document-list {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
}

.list-header {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 24px;
}

.list-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.list-header .count {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.document-item {
  padding: 20px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.document-item:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-item h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.document-item p {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.document-detail,
.document-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header,
.edit-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  gap: 12px;
}

.detail-content,
.edit-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.detail-content h1 {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 600;
}

.detail-content .meta {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.content {
  line-height: 1.6;
}

.content :deep(h1) {
  font-size: 24px;
  margin: 24px 0 16px 0;
}

.content :deep(h2) {
  font-size: 20px;
  margin: 20px 0 12px 0;
}

.content :deep(p) {
  margin: 12px 0;
}
</style>
