<template>
  <div class="document-detail" v-if="document">
    <!-- 文档头部 -->
    <div class="document-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="handleBack" size="small">
          返回
        </el-button>
        
        <div class="breadcrumb">
          <span class="category-name">{{ getCategoryName(document.categoryId) }}</span>
          <el-icon class="separator"><ArrowRight /></el-icon>
          <span class="document-title">{{ document.title }}</span>
        </div>
      </div>
      
      <div class="header-right">
        <el-button :icon="Edit" @click="handleEdit" type="primary">
          编辑
        </el-button>
        
        <el-dropdown @command="handleCommand">
          <el-button :icon="MoreFilled" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="copy">复制链接</el-dropdown-item>
              <el-dropdown-item command="export">导出文档</el-dropdown-item>
              <el-dropdown-item command="history">查看历史</el-dropdown-item>
              <el-dropdown-item divided command="delete" class="danger">
                删除文档
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 文档信息 -->
    <div class="document-info">
      <div class="info-main">
        <h1 class="title">{{ document.title }}</h1>
        
        <div class="meta-info">
          <div class="meta-item">
            <el-icon><User /></el-icon>
            <span>{{ document.author }}</span>
          </div>
          
          <div class="meta-item">
            <el-icon><Clock /></el-icon>
            <span>{{ formatTime(document.updatedAt) }}</span>
          </div>
          
          <div class="meta-item">
            <el-icon><View /></el-icon>
            <span>{{ document.viewCount }} 次浏览</span>
          </div>
          
          <div class="meta-item">
            <el-icon><Star /></el-icon>
            <span>{{ document.likeCount }} 个赞</span>
          </div>
          
          <div class="meta-item">
            <el-tag :type="getStatusType(document.status)" size="small">
              {{ getStatusText(document.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="tags-section" v-if="document.tags.length > 0">
          <el-tag
            v-for="tag in document.tags"
            :key="tag"
            class="tag"
            effect="plain"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
      
      <div class="info-actions">
        <el-button
          :icon="Star"
          :type="isLiked ? 'primary' : 'default'"
          @click="handleLike"
        >
          {{ isLiked ? '已赞' : '点赞' }}
        </el-button>
        
        <el-button :icon="Share" @click="handleShare">
          分享
        </el-button>
        
        <el-button :icon="Collection" @click="handleCollect">
          收藏
        </el-button>
      </div>
    </div>

    <!-- 文档内容 -->
    <div class="document-content">
      <div class="content-wrapper">
        <RichEditor
          :model-value="document.content"
          :editable="false"
          class="readonly-editor"
        />
      </div>
      
      <!-- 目录导航 -->
      <div class="toc-sidebar" v-if="tocItems.length > 0">
        <div class="toc-title">目录</div>
        <div class="toc-list">
          <div
            v-for="item in tocItems"
            :key="item.id"
            :class="['toc-item', `toc-level-${item.level}`]"
            @click="scrollToHeading(item.id)"
          >
            {{ item.text }}
          </div>
        </div>
      </div>
    </div>

    <!-- 相关文档 -->
    <div class="related-documents" v-if="relatedDocuments.length > 0">
      <h3 class="related-title">相关文档</h3>
      <div class="related-list">
        <div
          v-for="doc in relatedDocuments"
          :key="doc.id"
          class="related-item"
          @click="handleRelatedClick(doc)"
        >
          <div class="related-content">
            <h4 class="related-doc-title">{{ doc.title }}</h4>
            <p class="related-summary">{{ doc.summary }}</p>
            <div class="related-meta">
              <span>{{ doc.author }}</span>
              <span>{{ formatTime(doc.updatedAt) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论区域 -->
    <div class="comments-section">
      <h3 class="comments-title">评论 ({{ comments.length }})</h3>
      
      <div class="comment-form">
        <el-input
          v-model="newComment"
          type="textarea"
          :rows="3"
          placeholder="写下你的评论..."
          maxlength="500"
          show-word-limit
        />
        <div class="comment-actions">
          <el-button type="primary" @click="handleAddComment" :disabled="!newComment.trim()">
            发表评论
          </el-button>
        </div>
      </div>
      
      <div class="comments-list">
        <div
          v-for="comment in comments"
          :key="comment.id"
          class="comment-item"
        >
          <div class="comment-avatar">
            <el-avatar :size="32">{{ comment.author.charAt(0) }}</el-avatar>
          </div>
          <div class="comment-content">
            <div class="comment-header">
              <span class="comment-author">{{ comment.author }}</span>
              <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
            </div>
            <div class="comment-text">{{ comment.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ArrowLeft,
  ArrowRight,
  Edit,
  MoreFilled,
  User,
  Clock,
  View,
  Star,
  Share,
  Collection
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useKnowledgeStore } from '@/stores/counter'
import RichEditor from './RichEditor.vue'
import type { KnowledgeDocument } from '@/types/knowledge'

interface Props {
  document: KnowledgeDocument | null
}

interface Emits {
  (e: 'back'): void
  (e: 'edit', document: KnowledgeDocument): void
  (e: 'delete', document: KnowledgeDocument): void
}

interface TocItem {
  id: string
  text: string
  level: number
}

interface Comment {
  id: string
  author: string
  content: string
  createdAt: Date
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const knowledgeStore = useKnowledgeStore()

// 响应式数据
const isLiked = ref(false)
const newComment = ref('')
const comments = ref<Comment[]>([
  {
    id: '1',
    author: '张三',
    content: '这篇文档写得很详细，对我很有帮助！',
    createdAt: new Date('2024-01-20')
  },
  {
    id: '2',
    author: '李四',
    content: '建议增加一些实际的代码示例。',
    createdAt: new Date('2024-01-21')
  }
])

// 计算属性
const tocItems = computed<TocItem[]>(() => {
  if (!props.document?.content) return []
  
  // 简单的目录提取逻辑
  const headings = props.document.content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi) || []
  return headings.map((heading, index) => {
    const level = parseInt(heading.match(/<h([1-6])/)?.[1] || '1')
    const text = heading.replace(/<[^>]*>/g, '')
    return {
      id: `heading-${index}`,
      text,
      level
    }
  })
})

const relatedDocuments = computed(() => {
  if (!props.document) return []
  
  // 基于标签和分类查找相关文档
  return knowledgeStore.documents
    .filter(doc => 
      doc.id !== props.document!.id && (
        doc.categoryId === props.document!.categoryId ||
        doc.tags.some(tag => props.document!.tags.includes(tag))
      )
    )
    .slice(0, 3)
})

// 方法
const handleBack = () => {
  emit('back')
}

const handleEdit = () => {
  if (props.document) {
    emit('edit', props.document)
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'copy':
      navigator.clipboard.writeText(window.location.href)
      ElMessage.success('链接已复制到剪贴板')
      break
    case 'export':
      ElMessage.info('导出功能开发中...')
      break
    case 'history':
      ElMessage.info('历史记录功能开发中...')
      break
    case 'delete':
      if (props.document) {
        emit('delete', props.document)
      }
      break
  }
}

const handleLike = () => {
  isLiked.value = !isLiked.value
  if (props.document) {
    if (isLiked.value) {
      props.document.likeCount++
      ElMessage.success('已点赞')
    } else {
      props.document.likeCount--
      ElMessage.success('已取消点赞')
    }
  }
}

const handleShare = () => {
  ElMessage.info('分享功能开发中...')
}

const handleCollect = () => {
  ElMessage.info('收藏功能开发中...')
}

const handleRelatedClick = (document: KnowledgeDocument) => {
  // 这里应该触发父组件切换到相关文档
  ElMessage.info(`切换到文档: ${document.title}`)
}

const handleAddComment = () => {
  if (!newComment.value.trim()) return
  
  const comment: Comment = {
    id: Date.now().toString(),
    author: '当前用户',
    content: newComment.value.trim(),
    createdAt: new Date()
  }
  
  comments.value.unshift(comment)
  newComment.value = ''
  ElMessage.success('评论发表成功')
}

const scrollToHeading = (id: string) => {
  const element = document.getElementById(id)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const getCategoryName = (categoryId: string) => {
  const category = knowledgeStore.getCategoryById(categoryId)
  return category?.name || '未分类'
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'published':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

const formatTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style scoped>
.document-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.category-name {
  color: var(--el-color-primary);
}

.document-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.separator {
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-info {
  padding: 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.3;
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  font-size: 12px;
}

.info-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.readonly-editor {
  border: none;
}

.toc-sidebar {
  width: 240px;
  padding: 24px 16px;
  border-left: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
}

.toc-title {
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
}

.toc-item {
  padding: 6px 0;
  cursor: pointer;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  transition: color 0.2s;
  line-height: 1.4;
}

.toc-item:hover {
  color: var(--el-color-primary);
}

.toc-level-1 {
  font-weight: 500;
}

.toc-level-2 {
  padding-left: 12px;
}

.toc-level-3 {
  padding-left: 24px;
}

.related-documents {
  padding: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color-page);
}

.related-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.related-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.related-item {
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--el-bg-color);
}

.related-item:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.related-doc-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.related-summary {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.comments-section {
  padding: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
}

.comments-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.comment-form {
  margin-bottom: 24px;
}

.comment-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comment-item {
  display: flex;
  gap: 12px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.comment-author {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.comment-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.comment-text {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

:deep(.danger) {
  color: var(--el-color-danger);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-content {
    flex-direction: column;
  }
  
  .toc-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--el-border-color-lighter);
  }
  
  .document-info {
    flex-direction: column;
    gap: 16px;
  }
  
  .meta-info {
    gap: 16px;
  }
  
  .related-list {
    grid-template-columns: 1fr;
  }
}
</style>
