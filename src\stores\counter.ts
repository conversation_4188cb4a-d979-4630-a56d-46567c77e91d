import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { KnowledgeCategory, KnowledgeDocument, KnowledgeTag, SearchResult } from '@/types/knowledge'

export const useKnowledgeStore = defineStore('knowledge', () => {
  // 状态
  const categories = ref<KnowledgeCategory[]>([])
  const documents = ref<KnowledgeDocument[]>([])
  const tags = ref<KnowledgeTag[]>([])
  const currentDocument = ref<KnowledgeDocument | null>(null)
  const currentCategory = ref<KnowledgeCategory | null>(null)
  const searchResults = ref<SearchResult | null>(null)
  const loading = ref(false)

  // 计算属性
  const documentsByCategory = computed(() => {
    const map = new Map<string, KnowledgeDocument[]>()
    documents.value.forEach(doc => {
      if (!map.has(doc.categoryId)) {
        map.set(doc.categoryId, [])
      }
      map.get(doc.categoryId)!.push(doc)
    })
    return map
  })

  const recentDocuments = computed(() => {
    return documents.value
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 10)
  })

  const popularDocuments = computed(() => {
    return documents.value
      .sort((a, b) => b.viewCount - a.viewCount)
      .slice(0, 10)
  })

  // 方法
  const initializeData = () => {
    // 初始化示例数据
    categories.value = [
      {
        id: '1',
        name: '技术文档',
        description: '技术相关的文档和教程',
        icon: 'Document',
        color: '#409EFF',
        createdAt: new Date(),
        updatedAt: new Date(),
        children: [
          {
            id: '1-1',
            name: '前端开发',
            parentId: '1',
            icon: 'Monitor',
            color: '#67C23A',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: '1-2',
            name: '后端开发',
            parentId: '1',
            icon: 'Server',
            color: '#E6A23C',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
      },
      {
        id: '2',
        name: '运维手册',
        description: '运维相关的操作手册',
        icon: 'Setting',
        color: '#F56C6C',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '3',
        name: '产品文档',
        description: '产品需求和设计文档',
        icon: 'Notebook',
        color: '#909399',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    documents.value = [
      {
        id: '1',
        title: 'Vue 3 组件开发指南',
        content: '# Vue 3 组件开发指南\n\n这是一个关于Vue 3组件开发的详细指南...',
        summary: '详细介绍Vue 3组件的开发方法和最佳实践',
        categoryId: '1-1',
        tags: ['Vue3', '前端', '组件'],
        author: '张三',
        status: 'published',
        isPublic: true,
        viewCount: 156,
        likeCount: 23,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20'),
        lastEditedBy: '张三'
      },
      {
        id: '2',
        title: 'Docker 容器部署实践',
        content: '# Docker 容器部署实践\n\n本文档介绍如何使用Docker进行应用部署...',
        summary: 'Docker容器化部署的完整流程和注意事项',
        categoryId: '2',
        tags: ['Docker', '运维', '部署'],
        author: '李四',
        status: 'published',
        isPublic: true,
        viewCount: 89,
        likeCount: 15,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-18'),
        lastEditedBy: '李四'
      },
      {
        id: '3',
        title: 'API 接口设计规范',
        content: '# API 接口设计规范\n\n统一的API设计规范有助于提高开发效率...',
        summary: 'RESTful API设计的标准规范和最佳实践',
        categoryId: '1-2',
        tags: ['API', '后端', '规范'],
        author: '王五',
        status: 'published',
        isPublic: true,
        viewCount: 234,
        likeCount: 45,
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-22'),
        lastEditedBy: '王五'
      }
    ]

    tags.value = [
      { id: '1', name: 'Vue3', color: '#409EFF', count: 5 },
      { id: '2', name: '前端', color: '#67C23A', count: 8 },
      { id: '3', name: 'Docker', color: '#E6A23C', count: 3 },
      { id: '4', name: '运维', color: '#F56C6C', count: 6 },
      { id: '5', name: 'API', color: '#909399', count: 4 }
    ]
  }

  const searchDocuments = (query: string, categoryId?: string) => {
    loading.value = true

    setTimeout(() => {
      let filteredDocs = documents.value

      if (categoryId) {
        filteredDocs = filteredDocs.filter(doc => doc.categoryId === categoryId)
      }

      if (query) {
        filteredDocs = filteredDocs.filter(doc =>
          doc.title.toLowerCase().includes(query.toLowerCase()) ||
          doc.content.toLowerCase().includes(query.toLowerCase()) ||
          doc.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        )
      }

      searchResults.value = {
        documents: filteredDocs,
        total: filteredDocs.length,
        page: 1,
        pageSize: 20
      }

      loading.value = false
    }, 500)
  }

  const getDocumentById = (id: string) => {
    return documents.value.find(doc => doc.id === id)
  }

  const getCategoryById = (id: string): KnowledgeCategory | undefined => {
    const findInCategories = (cats: KnowledgeCategory[]): KnowledgeCategory | undefined => {
      for (const cat of cats) {
        if (cat.id === id) return cat
        if (cat.children) {
          const found = findInCategories(cat.children)
          if (found) return found
        }
      }
      return undefined
    }
    return findInCategories(categories.value)
  }

  const setCurrentDocument = (doc: KnowledgeDocument | null) => {
    currentDocument.value = doc
    if (doc) {
      // 增加浏览次数
      doc.viewCount++
    }
  }

  const setCurrentCategory = (category: KnowledgeCategory | null) => {
    currentCategory.value = category
  }

  return {
    // 状态
    categories,
    documents,
    tags,
    currentDocument,
    currentCategory,
    searchResults,
    loading,

    // 计算属性
    documentsByCategory,
    recentDocuments,
    popularDocuments,

    // 方法
    initializeData,
    searchDocuments,
    getDocumentById,
    getCategoryById,
    setCurrentDocument,
    setCurrentCategory
  }
})
