<template>
  <div class="basic-knowledge">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <h1 class="app-title">
          <el-icon><Notebook /></el-icon>
          知识库管理系统
        </h1>
      </div>

      <div class="navbar-right">
        <el-button type="primary" @click="showCreateDialog = true">
          新建文档
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div class="search-section">
          <el-input
            v-model="searchQuery"
            placeholder="搜索文档..."
            clearable
          />
        </div>

        <div class="categories-section">
          <h3>文档分类</h3>
          <div class="category-list">
            <div
              v-for="category in categories"
              :key="category.id"
              class="category-item"
              :class="{ active: selectedCategory?.id === category.id }"
              @click="selectCategory(category)"
            >
              📁 {{ category.name }}
              <span class="count">({{ getDocumentCount(category.id) }})</span>
            </div>
          </div>
        </div>

        <div class="stats-section">
          <h3>统计信息</h3>
          <div class="stats">
            <div class="stat-item">
              <span class="label">总文档数：</span>
              <span class="value">{{ documents.length }}</span>
            </div>
            <div class="stat-item">
              <span class="label">分类数：</span>
              <span class="value">{{ categories.length }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 文档列表 -->
        <div v-if="currentView === 'list'" class="document-list">
          <div class="list-header">
            <h2>{{ selectedCategory ? selectedCategory.name : '全部文档' }}</h2>
            <span class="count">{{ filteredDocuments.length }} 篇文档</span>
          </div>

          <div class="documents">
            <div
              v-for="doc in filteredDocuments"
              :key="doc.id"
              class="document-item"
              @click="viewDocument(doc)"
            >
              <h3>{{ doc.title }}</h3>
              <p>{{ doc.summary || '暂无摘要' }}</p>
              <div class="meta">
                <span>👤 {{ doc.author }}</span>
                <span>📅 {{ formatDate(doc.updatedAt) }}</span>
                <span>👁️ {{ doc.viewCount }} 次浏览</span>
              </div>
            </div>
          </div>

          <div v-if="filteredDocuments.length === 0" class="empty-state">
            <p>暂无文档</p>
            <el-button type="primary" @click="showCreateDialog = true">
              创建第一篇文档
            </el-button>
          </div>
        </div>

        <!-- 文档详情 -->
        <div v-else-if="currentView === 'detail'" class="document-detail">
          <div class="detail-header">
            <el-button @click="backToList">← 返回</el-button>
            <el-button type="primary" @click="editDocument">编辑</el-button>
          </div>

          <div class="detail-content">
            <h1>{{ currentDocument?.title }}</h1>
            <div class="meta">
              <span>👤 作者：{{ currentDocument?.author }}</span>
              <span>📅 更新时间：{{ formatDate(currentDocument?.updatedAt) }}</span>
              <span>👁️ 浏览次数：{{ currentDocument?.viewCount }}</span>
            </div>
            <div class="content">
              {{ currentDocument?.content }}
            </div>
          </div>
        </div>

        <!-- 文档编辑 -->
        <div v-else-if="currentView === 'edit'" class="document-edit">
          <div class="edit-header">
            <el-button @click="cancelEdit">取消</el-button>
            <el-button type="primary" @click="saveDocument">保存</el-button>
          </div>

          <div class="edit-content">
            <el-input
              v-model="editForm.title"
              placeholder="请输入文档标题"
              size="large"
              style="margin-bottom: 20px;"
            />

            <el-input
              v-model="editForm.content"
              type="textarea"
              :rows="20"
              placeholder="开始编写文档内容..."
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新建文档对话框 -->
    <el-dialog v-model="showCreateDialog" title="新建文档" width="500px">
      <el-form :model="createForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="createForm.title" placeholder="请输入文档标题" />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="createForm.categoryId" placeholder="选择分类">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="摘要">
          <el-input
            v-model="createForm.summary"
            type="textarea"
            :rows="3"
            placeholder="简要描述文档内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createDocument">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Notebook } from '@element-plus/icons-vue'

// 类型定义
interface Document {
  id: string
  title: string
  content: string
  summary: string
  categoryId: string
  author: string
  viewCount: number
  createdAt: Date
  updatedAt: Date
}

interface Category {
  id: string
  name: string
}

// 响应式数据
const currentView = ref<'list' | 'detail' | 'edit'>('list')
const searchQuery = ref('')
const selectedCategory = ref<Category | null>(null)
const currentDocument = ref<Document | null>(null)
const showCreateDialog = ref(false)

// 表单数据
const editForm = ref({
  title: '',
  content: '',
  categoryId: ''
})

const createForm = ref({
  title: '',
  categoryId: '',
  summary: ''
})

// 示例数据
const categories = ref<Category[]>([
  { id: '1', name: '技术文档' },
  { id: '2', name: '产品文档' },
  { id: '3', name: '运维手册' },
  { id: '4', name: '用户指南' }
])

const documents = ref<Document[]>([
  {
    id: '1',
    title: 'Vue 3 开发指南',
    content: `# Vue 3 开发指南

## 简介
Vue 3 是一个用于构建用户界面的渐进式框架。它在保持 Vue 2 易用性的同时，提供了更好的性能和更强大的功能。

## 主要特性
1. **Composition API** - 更好的逻辑复用和代码组织
2. **更好的性能** - 更小的包体积，更快的渲染
3. **更好的 TypeScript 支持** - 原生 TypeScript 支持
4. **新的内置组件** - Fragment、Teleport、Suspense

## 快速开始
使用 Vite 创建新项目：
\`\`\`bash
npm create vue@latest my-project
cd my-project
npm install
npm run dev
\`\`\`

## 基本用法
创建一个简单的 Vue 应用：
\`\`\`javascript
import { createApp } from 'vue'
import App from './App.vue'

createApp(App).mount('#app')
\`\`\`

这就是 Vue 3 的基本使用方法。`,
    summary: 'Vue 3 框架的完整开发指南，包含基本概念、主要特性和使用方法',
    categoryId: '1',
    author: '张三',
    viewCount: 156,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '2',
    title: '产品需求文档模板',
    content: `# 产品需求文档

## 产品概述
描述产品的基本信息、目标用户和核心价值。

## 功能需求
### 核心功能
1. 用户管理
2. 内容管理
3. 数据分析

### 辅助功能
1. 通知系统
2. 搜索功能
3. 导入导出

## 非功能需求
- 性能要求
- 安全要求
- 可用性要求

## 用户故事
作为一个用户，我希望能够...

## 验收标准
明确的验收标准和测试用例。`,
    summary: '标准的产品需求文档模板，包含功能需求、非功能需求和验收标准',
    categoryId: '2',
    author: '李四',
    viewCount: 89,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: '3',
    title: 'Docker 部署指南',
    content: `# Docker 部署指南

## 环境准备
确保系统已安装 Docker 和 Docker Compose。

## 构建镜像
\`\`\`bash
docker build -t myapp:latest .
\`\`\`

## 运行容器
\`\`\`bash
docker run -d -p 8080:80 myapp:latest
\`\`\`

## 使用 Docker Compose
创建 docker-compose.yml 文件：
\`\`\`yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:80"
  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
\`\`\`

## 常用命令
- 查看容器：docker ps
- 查看日志：docker logs <container_id>
- 进入容器：docker exec -it <container_id> bash`,
    summary: 'Docker 容器化部署的完整指南，包含镜像构建、容器运行和常用命令',
    categoryId: '3',
    author: '王五',
    viewCount: 234,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-22')
  }
])

// 计算属性
const filteredDocuments = computed(() => {
  let filtered = documents.value

  if (selectedCategory.value) {
    filtered = filtered.filter(doc => doc.categoryId === selectedCategory.value!.id)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(doc =>
      doc.title.toLowerCase().includes(query) ||
      doc.content.toLowerCase().includes(query) ||
      doc.summary.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
const selectCategory = (category: Category) => {
  selectedCategory.value = selectedCategory.value?.id === category.id ? null : category
}

const getDocumentCount = (categoryId: string) => {
  return documents.value.filter(doc => doc.categoryId === categoryId).length
}

const viewDocument = (doc: Document) => {
  currentDocument.value = doc
  // 增加浏览次数
  doc.viewCount++
  currentView.value = 'detail'
}

const backToList = () => {
  currentView.value = 'list'
  currentDocument.value = null
}

const editDocument = () => {
  if (currentDocument.value) {
    editForm.value = {
      title: currentDocument.value.title,
      content: currentDocument.value.content,
      categoryId: currentDocument.value.categoryId
    }
    currentView.value = 'edit'
  }
}

const cancelEdit = () => {
  if (currentDocument.value) {
    currentView.value = 'detail'
  } else {
    currentView.value = 'list'
  }
}

const saveDocument = () => {
  if (!editForm.value.title.trim()) {
    ElMessage.warning('请输入文档标题')
    return
  }

  if (currentDocument.value) {
    // 更新现有文档
    const index = documents.value.findIndex(d => d.id === currentDocument.value!.id)
    if (index > -1) {
      documents.value[index] = {
        ...documents.value[index],
        title: editForm.value.title,
        content: editForm.value.content,
        categoryId: editForm.value.categoryId,
        updatedAt: new Date()
      }
      currentDocument.value = documents.value[index]
    }
  }

  currentView.value = 'detail'
  ElMessage.success('文档保存成功')
}

const createDocument = () => {
  if (!createForm.value.title || !createForm.value.categoryId) {
    ElMessage.warning('请填写标题和分类')
    return
  }

  const newDoc: Document = {
    id: Date.now().toString(),
    title: createForm.value.title,
    content: '开始编写文档内容...',
    summary: createForm.value.summary,
    categoryId: createForm.value.categoryId,
    author: '当前用户',
    viewCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  documents.value.push(newDoc)
  showCreateDialog.value = false
  createForm.value = { title: '', categoryId: '', summary: '' }

  // 直接进入编辑模式
  currentDocument.value = newDoc
  editForm.value = {
    title: newDoc.title,
    content: newDoc.content,
    categoryId: newDoc.categoryId
  }
  currentView.value = 'edit'

  ElMessage.success('文档创建成功')
}

const formatDate = (date: Date | undefined) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  ElMessage.success('知识库系统加载成功！')
})
</script>

<style scoped>
.basic-knowledge {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.top-navbar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 20px;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 24px;
}

.categories-section,
.stats-section {
  margin-bottom: 24px;
}

.categories-section h3,
.stats-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 4px;
}

.category-item:hover {
  background-color: #e9ecef;
}

.category-item.active {
  background-color: #007bff;
  color: white;
}

.count {
  font-size: 12px;
  color: #6c757d;
}

.category-item.active .count {
  color: rgba(255, 255, 255, 0.8);
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.label {
  color: #6c757d;
  font-size: 14px;
}

.value {
  font-weight: 600;
  color: #007bff;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background-color: white;
}

.document-list {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
}

.list-header {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e9ecef;
}

.list-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #212529;
}

.list-header .count {
  font-size: 14px;
  color: #6c757d;
}

.document-item {
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: white;
}

.document-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.document-item h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.document-item p {
  margin: 0 0 12px 0;
  color: #6c757d;
  line-height: 1.5;
}

.meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6c757d;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state p {
  margin-bottom: 20px;
  font-size: 16px;
}

.document-detail,
.document-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header,
.edit-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  background-color: #f8f9fa;
}

.detail-content,
.edit-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.detail-content h1 {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 600;
  color: #212529;
}

.detail-content .meta {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.content {
  line-height: 1.8;
  color: #495057;
  white-space: pre-wrap;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .top-navbar {
    padding: 0 16px;
  }

  .app-title {
    font-size: 18px;
  }
}
</style>